<!-- 儿童 -->
<template>
  <view class="layout">
    <view class="search">
      <!-- @search="sendSearch" @custom="sendSearch" @clear="clear" -->
      <up-search
        placeholder="搜索儿童姓名..."
        v-model="title"
        bgColor="#FFFFFF"
        borderColor="#EEEEEE"
        :animation="true"
        :clearabled="true"
        @search="sendSearch"
        @custom="sendSearch"
        @clear="clear"
      />
    </view>
    <!-- 下拉筛选框 -->
    <view
      class="stuMgmtOp"
      style="margin: 30rpx 0"
    >
      <!-- <text class="f-w-500 f-s-28">全部学生</text> -->
      <up-dropdown
        class="dropdown"
        menu-icon-size="32rpx"
      >
        <up-dropdown-item
          v-model="state"
          :title="dropdownTitle"
          :options="options"
          @change="selectDropdown"
        />
      </up-dropdown>
      <text
        class="f-w-500 f-s-28 add"
        style="color: #3f79ff; margin-left: auto"
        @click="openPopup"
        >新增儿童</text
      >
    </view>
    <view
      class="col user-item flex"
      v-for="(item, index) in childrens"
      :key="index"
    >
      <view @tap="gotoPage(item.id)">
        <image
          :src="getChildAvatar(item)"
          mode="aspectFill"
        />
      </view>
      <view
        class="user-info"
        @tap="gotoPage(item.id)"
      >
        <view class="flex-ac">
          <text class="user-name">{{ item.title }}</text>
          <view style="margin-right: 16rpx">
            <up-icon
              v-if="item.sex == 1"
              name="/static/icon/male.png"
              size="28rpx"
            ></up-icon>
            <up-icon
              v-else
              name="/static/icon/female.png"
              size="28rpx"
            ></up-icon>
          </view>
          <text
            class="f-w-500 f-s-22"
            style="color: #808080"
            >{{ item.note }}</text
          >
        </view>
        <view>
          <text class="box-green">优势</text>
          <text
            class="f-w-400 f-s-24"
            style="color: #808080; margin-right: 12rpx"
            >请进行儿童评估</text
          >
          <text class="box-red">需关注</text>
          <text
            class="f-w-400 f-s-24"
            style="color: #808080; margin-right: 12rpx"
            >请进行儿童评估</text
          >
        </view>
        <!-- TODO: 后期添加 -->
        <!-- <view style="color: #808080">
                    <text class="f-w-500 f-s-24" style="margin-right: 8rpx">-</text>
                    <text class="f-w-400 f-s-24">-</text>
                    <text class="line" />
                    <text class="f-w-500 f-s-24" style="margin-right: 8rpx">-</text>
                    <text class="f-w-400 f-s-24">-</text>
                    <text class="line" />
                    <text class="f-w-500 f-s-24" style="margin-right: 8rpx">-</text>
                    <text class="f-w-400 f-s-24">-</text>
            </view> -->
      </view>

      <view style="display: flex; align-items: flex-start">
        <up-icon
          class="card-icon"
          size="36rpx"
          name="more-dot-fill"
          @tap="showPopup(item, index)"
        />
      </view>
    </view>
    <view
      v-if="isEmpty"
      class="empty"
      >暂无数据/检查有无儿童</view
    >
    <up-loading-icon
      :show="isLoading"
      mode="semicircle"
      style="margin-top: 200rpx"
    />

    <Popup
      :show="isPopup"
      @click="isPopup = false"
      @close="isPopup = false"
      :safeAreaInsetBottom="false"
    >
      <!-- <children-select></children-select> -->

      <view
        class="iconAction"
        v-if="isAction"
      >
        <view
          v-if="state == 0"
          class="returnIcon"
          @click="selectFn('返园')"
        >
          <image src="@/static/common/leaving.png"></image>
          <view class="">返园</view>
        </view>
        <view @click="editItem">
          <image src="@/static/common/editor.png"></image>
          <view class="">编辑</view>
        </view>
        <view @click="selectFn('删除')">
          <image src="@/static/common/delete.png"></image>
          <view class="">删除</view>
        </view>
        <view
          @click="selectFn('离园')"
          v-if="state == 1"
        >
          <image src="@/static/common/leaving.png"></image>
          <view class="">离园</view>
        </view>
      </view>

      <Children-add-student
        v-else
        @successSend="successSend"
        :editData="editData"
      />
    </Popup>
    <up-modal
      :show="isModal"
      contentTextAlign="center"
      @cancel="cancel"
      showCancelButton
      @confirm="selectModalFn"
      :asyncClose="true"
    >
      <view style="font-size: 28rpx">
        {{ isActionDelete ? "删除" : "请您确认是否将" }}
        <text class="delstyle">{{ _item.title }}</text>
        {{
          isActionDelete
            ? "时，所有相关资料（包括照片、视频、语言等）将被一并删除，请确认无误后再进行！"
            : isActionLeave
            ? "离园？"
            : isActionReturn
            ? "返园？"
            : ""
        }}
      </view>
    </up-modal>
  </view>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import {
  getChildrenList,
  deleteChildrenItem,
  putChildrenItem,
} from "@/api/children.js";
import ChildrenAddStudent from "./components/childrenAddStudent.vue";
let childrens = ref([]);
let title = ref("");
let isLoading = ref(false);
let isEmpty = ref(false);
let isPopup = ref(false);
let isAction = ref(true); // 是否显示操作
let _item = ref("");
let editData = ref({});
let state = ref(1);
let dropdownTitle = ref("在园");
let classId = ref(null);
let isModal = ref(false);
let isActionDelete = ref(false);
let isActionLeave = ref(false);
let isActionReturn = ref(false);
let searchContent = ref(""); // 搜索内容
let options = ref([
  {
    label: "在园",
    value: 1,
  },
  {
    label: "离园",
    value: 0,
  },
]);
let headList = [
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png",
];

// 处理头像地址，添加阿里云OSS图片处理参数
const getChildAvatar = (child) => {
  let avatarUrl = ''

  // 优先使用 headers 中的头像
  if (child?.headers && child.headers.length > 0 && child.headers[0]?.uri) {
    avatarUrl = child.headers[0].uri
  }
  // 根据性别使用默认头像
  else {
    avatarUrl = child.sex === 1 ? headList[0] : headList[1] // 1=男孩，2=女孩
  }

  // 直接在图片地址后面拼接OSS处理参数
  return avatarUrl + '?x-oss-process=image/resize,m_fill,w_600'
}

const props = defineProps({
  classId: {
    type: String || Number,
    default: "",
  },
});

// 搜索
// api start
const childrenList = async (title) => {
  isLoading.value = true;
  isEmpty.value = false;
  childrens.value = [];
  let form = {
    current: 1,
    pageSize: 999,
    state: state.value,
  };
  if (title) form.title = title;
  if (props.classId) form.classId = props.classId;
  const res = await getChildrenList(form);
  if (res.status == 0) {
    childrens.value = res.data;
  }
  isLoading.value = false;
};

// api end

watch(
  () => props.classId,
  (newVal) => {
    classId.value = newVal;
    childrenList();
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  classId.value = props.classId;
  childrenList();
});

const gotoPage = (id) => {
  uni.navigateTo({
    url: `/childrenDetails/homepage/homepage?id=${id}`,
  });
};

const selectModalFn = () => {
  if (isActionDelete.value) delItem();
  if (isActionLeave.value) leaveItem();
  if (isActionReturn.value) leaveItem(1);
};

const sendSearch = (item) => {
  console.log(item);
  searchContent.value = item;
  childrenList(item);
};
const clear = () => {
  title.value = "";
  childrenList();
};
// 新增成功后回调事件
const successSend = () => {
  isPopup.value = false;
  childrenList(searchContent.value);
};

// 点击新增学生
const openPopup = () => {
  isPopup.value = true;
  isAction.value = false;
  editData.value = {};
};

// 点击编辑
const editItem = () => {
  isPopup.value = false;
  editData.value = _item.value;
  setTimeout(() => {
    isPopup.value = true;
    isAction.value = false;
  }, 310);
};

// 点击儿童右边的三个点
const showPopup = (item, index) => {
  isPopup.value = true;
  isAction.value = true;
  _item.value = {};
  _item.value = { ...item, index };
};
// 删除一项儿童
const delItem = async () => {
  let { id, index } = _item.value;
  let res = await deleteChildrenItem(id);
  if (res.status == 0) {
    childrens.value.splice(index, 1);
    uni.$u.toast("删除成功");
    isPopup.value = false;
    isModal.value = false;
    isActionDelete.value = false;
  }
};

const cancel = () => {
  isModal.value = false;
  setTimeout(() => {
    isActionDelete.value = false;
    isActionLeave.value = false;
  }, 400);
};

const selectFn = (type) => {
  isModal.value = true;
  if (type == "删除") isActionDelete.value = true;
  if (type == "离园") isActionLeave.value = true;
  if (type == "返园") isActionReturn.value = true;
};

const leaveItem = async (state = 0) => {
  const { id, index } = _item.value;
  // 0 离园 1 正常
  let res = await putChildrenItem({
    id,
    state,
  });
  if (res.status == 0) {
    childrens.value.splice(index, 1);
    state == 1 ? uni.$u.toast("返园成功") : uni.$u.toast("离园成功");
    isPopup.value = false;
    isModal.value = false;
    isActionLeave.value = false;
    isActionReturn.value = false;
  }
};

const selectDropdown = (e) => {
  console.log(e);
  const result = options.value.filter((item) => item.value === e);
  console.log(result[0].label);
  dropdownTitle.value = result[0].label;
  childrenList();
};
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, //解除样式隔离
};
</script>

<style lang="scss" scoped>
.layout {
  min-height: 70vh;
  background-color: rgba(0, 0, 0, 0);
  // overflow-y: auto;
  box-sizing: border-box;
  // background: #f5f5f5;

  .stuMgmtOp {
    position: relative;
    ::v-deep .u-dropdown__menu__item {
      flex: initial;
      line-height: 80rpx;
    }
    .add {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 100;
    }
  }

  .iconAction {
    padding-bottom: env(safe-area-inset-bottom);
    .returnIcon {
      image {
        transform: rotate(180deg);
      }
    }
    & > view {
      height: 88rpx;
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      line-height: 48rpx;
      color: rgba(51, 51, 51, 1);
      text-align: left;
      vertical-align: middle;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 28rpx;
      }
    }
  }

  .empty {
    color: #ccc;
    font-weight: 600;
    font-size: 28rpx;
    text-align: center;
    margin-top: 200rpx;
  }

  .line {
    display: inline-block;
    width: 1px;
    height: 20rpx;
    background: #cccccc;
    margin: 0 12rpx;
  }

  .box-green {
    width: fit-content;
    box-sizing: border-box;
    // height: 28rpx;
    border-radius: 5.84rpx;
    background: rgba(84, 186, 106, 0.12);
    color: #54ba6a;
    padding: 6rpx;
    display: inline-block;
    font-size: 20rpx;
    font-weight: 500;
    margin-right: 8rpx;
  }

  .box-red {
    width: fit-content;
    box-sizing: border-box;
    // height: 28rpx;
    border-radius: 5.84rpx;
    background: rgba(237, 111, 114, 0.12);
    color: #ed6f72;
    padding: 6rpx;
    display: inline-block;
    font-size: 20rpx;
    font-weight: 500;
    margin-right: 8rpx;
  }

  .user-item {
    margin-bottom: 24rpx;

    .user-info {
      flex: 1;
    }

    .user-name {
      font-size: 30rpx;
      font-weight: 600;
      margin-right: 12rpx;
    }

    image {
      width: 88rpx;
      height: 88rpx;
      margin-right: 24rpx;
      border-radius: 20rpx;
      overflow: hidden;
    }
  }

  .user-item:last-child {
    margin-bottom: 28rpx;
  }

  .col {
    width: 100%;
    border-radius: 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    padding: 28rpx;
  }

  .delstyle {
    font-size: 30rpx;
    font-weight: 600;
    color: #367cff;
  }
}
</style>
