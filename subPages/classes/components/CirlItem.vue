<!-- 圈子 -->
<template>
  <view class="circle">
    <view class="circle-item">
      <view class="circle-item-top">
        <up-image
          class="imglab"
          :width="36"
          :height="36"
          shape="circle"
          mode="aspectFill"
          :src="getAuthorAvatar(comment)"
          :lazy-load="true"
        ></up-image>
        <view class="name">
          <text class="nic">{{comment.nickname || comment.username}}</text>
          <uni-icons
            type="more-filled"
            @click="showEdit"
            size="18"
            color="rgba(128, 128, 128, 1)"
          ></uni-icons>
        </view>
      </view>
      <view class="circle-info" @click="goToCommentsDetailPage">
        <view class="tit">
          <text class="tag" v-if="comment.status == 'draft'">未发布</text>
          <text
            class="circle-info-cont moment-tit"
            v-if="comment.type!='article'"
          >
            {{comment.title}}
          </text>
        </view>
        <view
          class="articleTit"
          v-if="comment.type=='article' && comment.title"
        >
          {{comment.title}}
        </view>
        <template v-if="comment.type!='article'">
          <view
            class="tit"
            v-if="comment.attachments&&comment.attachments.length != 0"
          >
            <text class="circle-info-cont moment-cont">
              {{comment.content}}
            </text>
          </view>
          <MomentImage :comment="comment" :canPlay="canPlay"/>
        </template>
        <view class="priz">
          <view class="time">
            <text v-if="comment.publishTime"
              >{{ formatDate(comment.publishTime, 'MM-DD HH:mm')}}</text
            >
          </view>
          <view>
            <uni-icons
              type="hand-up"
              size="18"
              color="rgba(153, 153, 153, 1)"
            ></uni-icons>
            <text class="priz-num">{{comment.likeCount || 0}}</text>
          </view>
        </view>
        <view class="cont prz" v-if="comment.likeCount>0">
          <uni-icons
            type="heart"
            size="18"
            color="rgba(153, 153, 153, 1)"
          ></uni-icons>
          <template v-for="(item,idx) in comment.likes" :key="idx">
            <text class="priz-num"
              >{{item.allRelationshipText}}
              {{idx==comment.likes.length?'':','}}</text
            >
          </template>
        </view>
      </view>
    </view>
  </view>
  <!-- 帖子操作弹窗 -->
  <PopTactons
    ref="popTactonsRef"
    @delMoment="delMoment"
    @editMoment="editMoment"
    @pinnedAction="pinnedAction"
  />
</template>
<script setup>
  import { reactive, ref, toRaw } from "vue";
  import { onShow, onShareAppMessage, onLoad } from "@dcloudio/uni-app";
  import PopTactons from "./PopTactons.vue";
  import MomentImage from "./MomentImage.vue";
  import { formatDate } from "@/utils/index.js";

  let props = defineProps({
    comment: {
      type: Object,
      default: () => ({}),
    },
    classTitle: {
      type: String,
      default: "",
    },
    canPlay: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(["editMoment", "delMoment", "pinnedAction"]);
  const popTactonsRef = ref(null);

  // 处理作者头像地址，添加阿里云OSS图片处理参数
  const getAuthorAvatar = (comment) => {
    let avatarUrl = comment.authorAvatar || 'https://s.mypacelab.com/202503/HtSjhmnWb9zv6988a04f615ad8977c0c79dd6fdd0aae.png'

    // 直接在图片地址后面拼接OSS处理参数
    return avatarUrl + '?x-oss-process=image/resize,m_fill,w_600'
  }
  // 展示帖子操作弹窗
  const showEdit = () => {
    const commentData = toRaw(props.comment);
    popTactonsRef.value.open(commentData);
  };
  // 删除帖子回调
  const delMoment = (id) => {
    emit("delMoment", id);
  };
  // 编辑帖子回调
  const editMoment = () => {
    emit("editMoment");
  };
  // 帖子置顶回调
  const pinnedAction = () => {
    emit("pinnedAction");
  };
  // 跳转帖子详情
  const goToCommentsDetailPage = () => {
    const pages = getCurrentPages();
    const currentPagePath = pages[pages.length - 1].route;
    if (currentPagePath.indexOf("subPages/classes/commentsdetails") > -1) {
      return;
    } else {
      uni.navigateTo({
        url: `/subPages/classes/commentsdetails?id=${props.comment.id}&classTitle=${props.classTitle}`,
      });
    }
  };
</script>

<style lang="scss" scoped>
  .circle {
    @include selfshaow;
    padding: 28upx;
    margin: 20upx 30upx 0 30upx;
    background-color: #fff;
    border-radius: 28upx;

    .circle-item {
      .circle-item-top {
        @include selflex(x, start, center);
        ::v-deep .u-image{
          display: inline-flex;
        }
        .name {
          @include selflex(x, between, center);
          margin-left: 20upx;
          flex-grow: 1;

          .nic {
            font-size: 30upx;
            font-weight: 600;
            letter-spacing: 0upx;
            line-height: 36upx;
            color: rgba(51, 51, 51, 1);
          }
        }
      }

      .circle-info {
        .time {
          line-height: 34rpx;
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
        }

        .tit {
          @include selflex(x, start, start);
          // align-items: baseline;
          font-size: 28upx;
          font-weight: 400;
          letter-spacing: 0upx;
          line-height: 48upx;
          color: rgba(51, 51, 51, 1);
          margin: 12upx 0;

          .tag {
            display: inline-flex;
            flex-shrink: 0;
            font-size: 20upx;
            font-weight: 500;
            line-height: 1;
            color: rgba(255, 255, 255, 1);
            border-radius: 6upx;
            background: rgba(255, 154, 59, 1);
            padding: 6upx;
            margin-right: 10upx;
            margin-top: 8upx;
          }

          .circle-info-cont {
            word-break: break-all;
            white-space: pre-line;

            &.moment-tit {
              font-weight: 600;
            }
            &.moment-cont {
              font-size: 28upx;
            }
          }
        }
        .articleTit {
          border-radius: 12upx;
          background: rgba(245, 245, 245, 1);
          padding: 14upx;
          font-size: 28upx;
          font-weight: 400;
          color: rgba(51, 51, 51, 1);
          height: 100upx;
          box-sizing: border-box;
          line-height: 70upx;
        }

        .priz {
          @include selflex(x, between, center);
          font-size: 24upx;
          font-weight: 400;
          letter-spacing: 0upx;
          line-height: 30upx;
          color: rgba(153, 153, 153, 1);
          padding-top: 26upx;
          padding-bottom: 26upx;
        }

        .priz-num {
          margin-left: 4upx;
          font-size: 24upx;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 24upx;
          color: rgba(128, 128, 128, 1);
        }
      }
    }
  }
</style>
