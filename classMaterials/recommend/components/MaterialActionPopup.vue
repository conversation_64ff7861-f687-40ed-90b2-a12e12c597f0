<template>
  <Popup :show="show" @close="show = false">
    <view class="pop-item" @click.stop="handleNotInterested">
      <up-icon name="close" color="rgba(51, 51, 51, 1)" size="16"></up-icon>
      <text class="act">不感兴趣</text>
    </view>
    <view class="pop-item" @click.stop="handleViewDetail">
      <up-icon name="eye" color="rgba(51, 51, 51, 1)" size="16"></up-icon>
      <text class="act">详情</text>
    </view>
    <u-gap height="20"></u-gap>
  </Popup>
  
  <!-- 确认弹窗 -->
  <up-modal
    :show="isConfirmModal"
    title="确认操作"
    showCancelButton
    @cancel="isConfirmModal = false"
    @confirm="confirmNotInterested"
  >
    <view class="modal-content">确定不感兴趣此材料吗？</view>
  </up-modal>
</template>

<script setup>
import { ref } from 'vue'
import Popup from '@/components/Popup/Popup.vue'

const emit = defineEmits(['notInterested', 'viewDetail'])
const show = ref(false)
const currentItem = ref({})
const currentIndex = ref(-1)
const isConfirmModal = ref(false)

const open = (item, index) => {
  show.value = true
  currentItem.value = item
  currentIndex.value = index
}

const handleNotInterested = () => {
  show.value = false
  isConfirmModal.value = true
}

const handleViewDetail = () => {
  show.value = false
  emit('viewDetail', currentItem.value)
}

const confirmNotInterested = () => {
  isConfirmModal.value = false
  emit('notInterested', currentIndex.value)
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.pop-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  font-size: 30rpx;
  color: rgba(51, 51, 51, 1);
  
  .act {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: rgba(51, 51, 51, 1);
  }
  
  &:active {
    background: rgba(0, 0, 0, 0.05);
  }
}

.modal-content {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
}
</style>
