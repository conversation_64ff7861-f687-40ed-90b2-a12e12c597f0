<template>
  <view class="custom-layout">
    <!-- 顶部导航栏 -->
    <up-navbar
      title="班级材料推荐"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    >
    </up-navbar>

    <view class="main-container">
      <!-- 班级信息 -->
      <view class="class-info">
        <view class="class-info-content">
          <view class="class-name">{{ classInfo.title || '大一班' }}</view>
        </view>
      </view>

      <!-- Tab 切换 -->
      <view class="tab-container">
        <up-tabs
          :list="tabList"
          :current="currentTabIndex"
          @change="onTabChange"
          :scrollable="false"
          lineColor="#3f79ff"
          :activeStyle="{ color: '#3f79ff', fontWeight: '600' }"
          :inactiveStyle="{ color: '#666', fontWeight: '400' }"
          itemStyle="padding: 24rpx 0"
        ></up-tabs>
      </view>

      <!-- 描述信息 -->
      <view class="desc-container">
        <view class="class-desc" v-if="currentTabIndex === 0">
          AI基于班级核心经验和材料类别进行盖率度以及班级幼儿发展情况进行评分，推荐可采购的材料，点击不感兴趣即可移除。
        </view>
        <view class="class-desc" v-if="currentTabIndex === 1">
          AI基于班级核心经验和材料类别覆盖程度以及班级幼儿发展情况进行评分，推荐可从园区仓库领用的材料，点击不感兴趣可移除
        </view>
      </view>

      <!-- 材料列表 -->
      <view class="material-list">
        <scroll-view scroll-y class="material-scroll" @scrolltolower="loadMore">
          <view v-if="materialList.length > 0" class="list-content">
            <view
              v-for="(item, index) in materialList"
              :key="item.id || index"
              class="material-item"
            >
              <!-- 左侧内容 -->
              <view class="material-left">
                <image
                  class="material-image"
                  :src="item.selectedImg || '/static/game/placeholder.png'"
                  mode="aspectFill"
                  :lazy-load="true"
                />
                <view class="material-info">
                  <view class="material-name">{{ item.name || '梦幻城堡积木' }}</view>
                  <view class="material-area">{{ item.area || '积木区' }}</view>
                  <view class="recommend-reason">
                    <text class="reason-label">推荐理由：</text>
                    <text class="reason-text">{{
                      item.recommendReason || '提升幼儿推理能力'
                    }}</text>
                  </view>
                </view>
              </view>

              <!-- 右侧操作图标 -->
              <view class="material-right">
                <view class="action-icon" @click="showActionPopup(item, index)">
                  <uni-icons
                    type="more-filled"
                    size="18"
                    color="rgba(128, 128, 128, 1)"
                  ></uni-icons>
                </view>
              </view>
            </view>

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-more">
              <up-loading-icon mode="spinner" size="24"></up-loading-icon>
              <text class="loading-text">加载中...</text>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <view class="empty-text">暂无推荐材料</view>
          </view>
        </scroll-view>
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-actions">
        <view class="action-btn-group">
          <view class="secondary-btn" @click="reRecommend">
            <text>重新推荐</text>
          </view>
          <view class="secondary-btn" @click="exportList">
            <text>导出</text>
          </view>
          <view class="primary-btn" @click="createPurchaseOrder">
            <text>创建采购单({{ selectedCount }})</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 材料操作弹窗 -->
    <MaterialActionPopup
      ref="materialActionPopupRef"
      @notInterested="handleNotInterested"
      @viewDetail="handleViewDetail"
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRecommendMaterial } from '../api/index.js'
import { getclassList } from '@/api/index.js'
import MaterialActionPopup from './components/MaterialActionPopup.vue'

// 响应式数据
const classInfo = ref({
  title: '大一班',
  schoolTitle: '示例幼儿园'
})

const currentTabIndex = ref(0)
const materialList = ref([])
const selectedCount = ref(0)
const loading = ref(false)

// 页面参数
const classId = ref('')
const schoolId = ref('')
const pageSize = ref(10)
const currentPage = ref(1)

// 弹窗引用
const materialActionPopupRef = ref(null)

// Tab 配置 - 使用up-tabs组件格式
const tabList = reactive([
  {
    name: '采购材料推荐',
    key: 'cg',
    recommendType: 'cg',
    badge: { value: 0, bgColor: '#ff4757' }
  },
  {
    name: '园内材料推荐',
    key: 'yn',
    recommendType: 'yn',
    badge: { value: 0, bgColor: '#ff4757' }
  }
  // {
  //   name: '不使用材料识别',
  //   key: 'bsy',
  //   recommendType: 'bsy'
  // }
])

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '梦幻城堡积木',
    area: '积木区',
    reason: '提升幼儿推理能力',
    image: '/static/game/placeholder.png'
  },
  {
    id: 2,
    name: '梦幻城堡积木',
    area: '积木区',
    reason: '提升幼儿推理能力',
    image: '/static/game/placeholder.png'
  },
  {
    id: 3,
    name: '梦幻城堡积木',
    area: '积木区',
    reason: '提升幼儿推理能力',
    image: '/static/game/placeholder.png'
  },
  {
    id: 4,
    name: '梦幻城堡积木',
    area: '积木区',
    reason: '提升幼儿推理能力',
    image: '/static/game/placeholder.png'
  }
]

// 页面加载
onLoad((options) => {
  console.log('页面参数:', options)

  // 获取用户信息
  const userInfo = uni.getStorageSync('USER_INFO')
  if (userInfo) {
    classId.value = userInfo.currentClassId || options.classId || ''
    schoolId.value = userInfo.currentSchoolId || options.schoolId || ''
  }

  // 获取班级信息
  getClassInfo()
})

onMounted(() => {
  loadMaterialList(false)
})

// 方法
const onTabChange = (item, index) => {
  currentTabIndex.value = index
  console.log('切换到tab:', item.key, index)
  currentPage.value = 1 // 重置页码
  loadMaterialList(false) // 传入isLoadMore=false
}

const getClassInfo = async () => {
  try {
    if (schoolId.value) {
      const classListRes = await getclassList({ schoolId: schoolId.value })
      if (classListRes.status === 0 && classListRes.data) {
        const currentClass = classListRes.data.find((item) => item.id === classId.value)
        if (currentClass) {
          classInfo.value.title = currentClass.title
          classInfo.value.schoolTitle = currentClass.schoolTitle
        }
      }
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
    // 失败时使用默认数据
    if (classId.value) {
      classInfo.value.title = `班级${classId.value}`
    }
  }
}

const loadMaterialList = async (isLoadMore = false) => {
  if (!classId.value || !schoolId.value) {
    console.warn('缺少必要参数: classId 或 schoolId')
    return
  }

  const currentTab = tabList[currentTabIndex.value]
  if (!currentTab) return

  try {
    if (!isLoadMore) {
      uni.showLoading({ title: '加载中...' })
    }

    // 触底加载时增加size
    const currentSize = isLoadMore ? materialList.value.length + pageSize.value : pageSize.value

    const params = {
      classId: classId.value,
      schoolId: schoolId.value,
      size: currentSize,
      recommendType: currentTab.recommendType
    }

    console.log('请求参数:', params)
    const res = await getRecommendMaterial(params)

    if (res && res.data) {
      materialList.value = res.data

      // 更新选中数量为总数据长度
      selectedCount.value = res.data.length

      // 更新tab的badge数量
      currentTab.badge = { value: res.data.length, bgColor: '#ff4757' }
    }

    if (!isLoadMore) {
      uni.hideLoading()
    }
  } catch (error) {
    console.error('获取推荐材料失败:', error)
    if (!isLoadMore) {
      uni.hideLoading()
    }
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })

    // 出错时使用模拟数据
    materialList.value = [...mockData]
    selectedCount.value = mockData.length
  }
}

const loadMore = async () => {
  if (loading.value) return

  loading.value = true
  console.log('触底加载更多')

  try {
    await loadMaterialList(true) // 传入isLoadMore=true
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 显示操作弹窗
const showActionPopup = (item, index) => {
  materialActionPopupRef.value.open(item, index)
}

// 处理不感兴趣
const handleNotInterested = (index) => {
  materialList.value.splice(index, 1)
  selectedCount.value = Math.max(0, selectedCount.value - 1)
  uni.showToast({
    title: '已移除',
    icon: 'success'
  })
}

// 处理查看详情
const handleViewDetail = (item) => {
  // 参考classMaterials/index.vue的跳转方式
  uni.navigateTo({
    url: `/classMaterials/components/details?id=${item.id}&sourceType=${item.sourceType || 'toy'}`
  })
}

const reRecommend = () => {
  uni.showLoading({
    title: '重新推荐中...'
  })
  setTimeout(() => {
    loadMaterialList()
    uni.hideLoading()
    uni.showToast({
      title: '推荐已更新',
      icon: 'success'
    })
  }, 1500)
}

const exportList = () => {
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}

const createPurchaseOrder = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择材料',
      icon: 'none'
    })
    return
  }

  uni.showToast({
    title: '创建采购单功能开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.custom-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88rpx); /* 减去导航栏高度 */
  position: relative;
  overflow: hidden;
}
// 班级信息
.class-info {
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  .class-info-content {
    .class-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }
}

// 描述信息
.desc-container {
  padding: 0 32rpx 24rpx 32rpx;
  margin-bottom: 16rpx;

  .class-desc {
    font-size: 24rpx;
    color: rgba(128, 128, 128, 1);
    line-height: 1.5;
  }
}

// Tab 切换
.tab-container {
  padding: 0 32rpx;
  margin-bottom: 16rpx;
}

// 材料列表
.material-list {
  flex: 1;
  overflow: hidden;
  position: relative;

  .material-scroll {
    height: 100%;
    padding-bottom: 120rpx; /* 为底部按钮预留空间 */
    box-sizing: border-box;
  }

  .list-content {
    padding: 0 32rpx 40rpx 32rpx;
  }

  .material-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background: #fff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    border-radius: 28rpx;
    margin-bottom: 20rpx;

    &:last-child {
      border-bottom: none;
    }

    .material-left {
      flex: 1;
      display: flex;
      align-items: flex-start;
      gap: 24rpx;

      .material-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        background: #f5f5f5;
        flex-shrink: 0;
      }

      .material-info {
        flex: 1;

        .material-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }

        .material-area {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 16rpx;
        }

        .recommend-reason {
          .reason-label {
            font-size: 24rpx;
            color: #ff8c00;
            font-weight: 500;
          }

          .reason-text {
            font-size: 24rpx;
            color: #ff8c00;
          }
        }
      }
    }

    .material-right {
      display: flex;
      align-items: center;
      justify-content: center;

      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 50%;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    gap: 16rpx;

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}

// 底部按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #eee;
  z-index: 100;

  .action-btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;

    .secondary-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 32rpx;
      background: #f5f5f5;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        background: #e8e8e8;
      }
    }

    .primary-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 32rpx;
      background: #3f79ff;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        background: #2c5dd9;
      }
    }
  }
}
</style>
