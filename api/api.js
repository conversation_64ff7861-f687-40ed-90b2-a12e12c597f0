import { request } from "@/common/request.js"

// 查询课程列表
export function getCourseList(data) {
	return request({
		url: '/business/subject/list_v2',
		method: 'get',
		data
	})
}

export async function fetchUploadToken() {
	return request({
		url: '/business/resource/auth',
		method: 'get',
	})
}

// 查询 - STS授权数据
export function fileauthV4(data) {
    return request({
        url: `/business/resource/post_v4`,
        method: "get",
        data
    })
}

export function generateSignature(data) {
    return request({
        // url: `/global/file/generateSignature`,
        url: `/jsapi/global/file/generateSignature`,
        method: "get",
        data,
		isGLobal: true
    })
}


export async function fetchResourceByHash(body) {
 return request({
 	url: '/business/resource/hash',
 	method: 'POST',
	data: body
 })
}

export async function createResource(data) {
	return request({
		url: '/business/resource/sync',
		method: 'POST',
		data
	})
}



// 删除一项课程
export function deleteItemList(data) {
	return request({
		url: `/jsapi/business/subject/delete/${data.id}?forceFlag=${data.forceFlag}`,
		// url: `/business/subject/${data}`,
		method: 'post'
	})
}

// 新增课程
export function addCourse(data) {
	return request({
		url: `/business/subject`,
		method: 'post',
		data
	})
}

// 修改课程
export function updataCourse(data) {
	return request({
		url: `/business/subject/u`,
		method: 'PUT',
		data
	})
}


// 获取家长列表
export function getParentList(data) {
	return request({
		url: '/jsapi/business/parent/list/v2',
		method: 'GET',
		data
	})
}


// 根据家长ID和儿童id查询家长详情

export function getParentDetail(data) {
	return request({
		url: '/jsapi/business/parent/detail',
		method: 'GET',
		data
	})
}

// 根据家长ID查询详情 /business/parent/{parentId}
export function getParentDetailById(parentId) {
	return request({
		url: `/jsapi/business/parent/${parentId}`,
		method: 'GET'
	})
}



export function updateParentDetail(data) {
	return request({
		url: '/jsapi/business/parent/update',
		method: 'post',
		data
	})
}

export function addParentDetail(data) {
	return request({
		url: '/jsapi/business/parent/add',
		method: 'post',
		data
	})
}
// 获取课程类别
export function getCategoryList(data) {
	return request({
		url: '/business/dictionary/list',
		method: 'GET',
		data
	})
}


// 获取单个儿童动态  获取历程(childId,日期范围)  /business/childjourney/detail
export function getChildrenDynamic(data) {
	return request({
		url: '/jsapi/business/childjourney/detail',
		method: 'POST',
		data
	})
}



// 获取班级儿童动态 获取历程(班级id,日期范围) /business/childjourney/classDetail
export function getClassDynamic(data) {
	return request({
		url: '/jsapi/business/childjourney/classDetail',
		method: 'POST',
		data
	})
}

// 获取时间范围获取学生历程统计信息  /business/childjourney/detail
export function getChildrenDynamicCount(data) {
	return request({
		url: '/jsapi/business/childjourney/detail',
		method: 'POST',
		data
	})
}
