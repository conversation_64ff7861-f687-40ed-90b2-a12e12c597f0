{"easycom": {"autoscan": true, "custom": {"^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}}, "pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "首页", "navigationStyle": "custom"}}, {"path": "pages/course/courseV1", "style": {"navigationBarTitleText": "课程", "navigationStyle": "custom"}}, {"path": "pages/login/login", "style": {"navigationBarTitleText": "登录", "navigationStyle": "custom"}}, {"path": "pages/game/index", "style": {"navigationBarTitleText": "游戏", "navigationStyle": "custom"}}, {"path": "pages/user/user", "style": {"navigationBarTitleText": "我的", "navigationStyle": "custom"}}, {"path": "pages/classes/indexV1", "style": {"navigationBarTitleText": "班级", "navigationStyle": "custom"}}, {"path": "pages/authentication/authentication", "style": {"navigationBarTitleText": "身份验证", "navigationStyle": "custom"}}, {"path": "pages/login/authorizeLogin", "style": {"navigationBarTitleText": "身份验证", "navigationStyle": "custom"}}], "subPackages": [{"root": "subPages/classes", "pages": [{"path": "circle", "style": {"navigationBarTitleText": "班级", "navigationStyle": "custom", "onReachBottomDistance": 100}}, {"path": "circleV1", "style": {"navigationBarTitleText": "班级", "navigationStyle": "custom", "onReachBottomDistance": 100}}, {"path": "post", "style": {"navigationBarTitleText": "发帖", "navigationStyle": "custom"}}, {"path": "postType", "style": {"navigationBarTitleText": "添加图片至文章", "navigationStyle": "custom"}}, {"path": "commentsdetails", "style": {"navigationBarTitleText": "帖子详情", "navigationStyle": "custom"}}, {"path": "choseTheme", "style": {"navigationBarTitleText": "选择主题", "navigationStyle": "custom"}}, {"path": "choseAct", "style": {"navigationBarTitleText": "选择活动", "navigationStyle": "custom"}}]}, {"root": "courseDetails", "pages": [{"path": "list/list", "style": {"navigationBarTitleText": "库列表", "navigationStyle": "custom"}}, {"path": "fnlist/fnlist", "style": {"navigationBarTitleText": "功能列表", "navigationStyle": "custom"}}, {"path": "list/draftList", "style": {"navigationBarTitleText": "草稿列表", "navigationStyle": "custom"}}, {"path": "classHome/classHome", "style": {"navigationBarTitleText": "活动首页"}}, {"path": "classHome/classHomeV1", "style": {"navigationBarTitleText": "课程详情"}}, {"path": "classHome/classText/classText", "style": {"navigationBarTitleText": "课程文本", "navigationStyle": "custom"}}, {"path": "classHome/coreStat/coreStat", "style": {"navigationBarTitleText": "核心经验统计", "navigationStyle": "custom"}}, {"path": "editActivity/editActivity", "style": {"navigationBarTitleText": "编辑活动"}}, {"path": "editClassText/editClassText", "style": {"navigationBarTitleText": "编辑课程文本"}}, {"path": "activityDetails/activityDetails", "style": {"navigationBarTitleText": "活动详情"}}, {"path": "evaluate/evaluate", "style": {"navigationBarTitleText": "编辑评价"}}, {"path": "addActivities/addActivities", "style": {"navigationBarTitleText": "新增活动"}}, {"path": "aiWrite/aiWrite", "style": {"navigationBarTitleText": "AI生成课程", "navigationStyle": "custom"}}, {"path": "aiWriteTemplatel/aiWriteTemplatel", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "aiCreateDetails/aiCreateDetails", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "activityDetails/evaluateMarking", "style": {"navigationBarTitleText": "打标", "navigationStyle": "custom"}}, {"path": "search/search", "style": {"navigationBarTitleText": "搜索"}}, {"path": "AIThemeBook/AIThemeBook", "style": {"navigationBarTitleText": "AI主题书", "navigationStyle": "custom"}}]}, {"root": "subPages/aiAssistant", "pages": [{"path": "aiTemplateList/index", "style": {"navigationBarTitleText": "AI模板列表", "navigationStyle": "custom"}}, {"path": "aiTemplate/index", "style": {"navigationBarTitleText": "AI模板", "navigationStyle": "custom"}}, {"path": "chat/index", "style": {"navigationBarTitleText": "AI聊天", "navigationStyle": "custom"}}, {"path": "chatRecord/index", "style": {"navigationBarTitleText": "历史记录", "navigationStyle": "custom"}}, {"path": "mindMap/index", "style": {"navigationBarTitleText": "思维导图", "navigationStyle": "custom"}}]}, {"root": "subPages/meeting", "pages": [{"path": "index", "style": {"navigationBarTitleText": "会议管理", "navigationStyle": "custom"}}, {"path": "meetingManager/index", "style": {"navigationBarTitleText": "会议管理", "navigationStyle": "custom"}}, {"path": "meetingDetail/index", "style": {"navigationBarTitleText": "会议详情", "navigationStyle": "custom"}}, {"path": "teacherChildInteraction/index", "style": {"navigationBarTitleText": "师幼互动评价", "navigationStyle": "custom"}}, {"path": "teacherChildInteraction/add", "style": {"navigationBarTitleText": "新增师幼互动", "navigationStyle": "custom"}}, {"path": "teacherChildInteraction/detail", "style": {"navigationBarTitleText": "师幼互动详情", "navigationStyle": "custom"}}]}, {"root": "childCoreAssessment", "pages": [{"path": "index/index", "style": {"navigationBarTitleText": "儿童核心经验评价", "navigationStyle": "custom"}}, {"path": "batchAssessment/batchAssessment", "style": {"navigationBarTitleText": "核心经验批量评价", "navigationStyle": "custom"}}]}, {"root": "childrenDetails", "pages": [{"path": "homepage/homepage", "style": {"navigationBarTitleText": "", "navigationStyle": "custom"}}, {"path": "updateAvatar/updateAvatar", "style": {"navigationBarTitleText": "上传头像", "navigationStyle": "custom"}}, {"path": "updateAvatar/updateFace", "style": {"navigationBarTitleText": "上传人脸", "navigationStyle": "custom"}}]}, {"root": "observation", "pages": [{"path": "index", "style": {"navigationBarTitleText": "观察记录", "navigationStyle": "custom"}}, {"path": "updateObservation", "style": {"navigationBarTitleText": "观察记录", "navigationStyle": "custom"}}, {"path": "observationRecord", "style": {"navigationBarTitleText": "我的记录", "navigationStyle": "custom"}}]}, {"root": "feedback", "pages": [{"path": "index/index", "style": {"navigationBarTitleText": "反馈列表", "navigationStyle": "custom"}}, {"path": "addfeedback/addfeedback", "style": {"navigationBarTitleText": "新增反馈", "navigationStyle": "custom"}}]}, {"root": "privacyPolicy", "pages": [{"path": "privacy/privacyPolicy", "style": {"navigationBarTitleText": "反馈列表", "navigationStyle": "custom"}}, {"path": "updatePwd/updatePwd", "style": {"navigationBarTitleText": "修改密码"}}]}, {"root": "regionalManagement", "pages": [{"path": "index", "style": {"navigationBarTitleText": "区域管理", "navigationStyle": "custom"}}, {"path": "components/add", "style": {"navigationBarTitleText": "新增区域", "navigationStyle": "custom"}}]}, {"root": "classMaterials", "pages": [{"path": "index", "style": {"navigationBarTitleText": "班级材料", "navigationStyle": "custom"}}, {"path": "components/add", "style": {"navigationBarTitleText": "新增材料", "navigationStyle": "custom"}}, {"path": "components/details", "style": {"navigationBarTitleText": "材料详情", "navigationStyle": "custom"}}, {"path": "recommend/index", "style": {"navigationBarTitleText": "材料推荐", "navigationStyle": "custom"}}]}, {"root": "childrenInArea", "pages": [{"path": "index", "style": {"navigationBarTitleText": "幼儿进区", "navigationStyle": "custom"}}]}, {"root": "parent", "pages": [{"path": "index", "style": {"navigationBarTitleText": "家长列表", "navigationStyle": "custom"}}]}, {"root": "guidePage", "pages": [{"path": "guidePage", "style": {"navigationBarTitleText": "幼儿进区", "navigationStyle": "custom"}}, {"path": "changeNickname/changeNickname", "style": {"navigationBarTitleText": "修改昵称", "navigationStyle": "custom"}}]}, {"root": "oneToOneListening", "pages": [{"path": "index", "style": {"navigationBarTitleText": "表征作品/倾听记录", "navigationStyle": "custom"}}, {"path": "updateOneToOneListening", "style": {"navigationBarTitleText": "表征作品/倾听", "navigationStyle": "custom"}}, {"path": "detail", "style": {"navigationBarTitleText": "表征作品/倾听详情", "navigationStyle": "custom"}}]}, {"root": "empiricalStatistics", "pages": [{"path": "index", "style": {"navigationBarTitleText": "核心材料经验统计", "navigationStyle": "custom"}}]}, {"root": "childrenAnalysis", "pages": [{"path": "index", "style": {"navigationBarTitleText": "幼儿进区统计", "navigationStyle": "custom"}}]}, {"root": "AImaterialReport", "pages": [{"path": "index", "style": {"navigationBarTitleText": "AI材料报告", "navigationStyle": "custom"}}]}, {"root": "weeklyReport", "pages": [{"path": "index", "style": {"navigationBarTitleText": "周报", "navigationStyle": "custom"}}]}, {"root": "childrenAttendance", "pages": [{"path": "index", "style": {"navigationBarTitleText": "出勤记录", "navigationStyle": "custom"}}, {"path": "addAttendance", "style": {"navigationBarTitleText": "出勤打卡", "navigationStyle": "custom"}}, {"path": "attendanceRecordDetail", "style": {"navigationBarTitleText": "出勤记录详情", "navigationStyle": "custom"}}]}, {"root": "childrenDynamic", "pages": [{"path": "select", "style": {"navigationBarTitleText": "选择幼儿", "navigationStyle": "custom"}}, {"path": "index", "style": {"navigationBarTitleText": "发动态", "navigationStyle": "custom"}}, {"path": "class", "style": {"navigationBarTitleText": "班级幼儿动态", "navigationStyle": "custom"}}, {"path": "childrensWords", "style": {"navigationBarTitleText": "童言童语", "navigationStyle": "custom"}}, {"path": "photos", "style": {"navigationBarTitleText": "照片", "navigationStyle": "custom"}}]}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#3F79FF", "borderStyle": "black", "backgroundColor": "#fff", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/tabbar/index.png", "selectedIconPath": "static/tabbar/activeIndex.png"}, {"pagePath": "pages/classes/indexV1", "text": "班级", "iconPath": "static/tabbar/class.png", "selectedIconPath": "static/tabbar/activeClass.png"}, {"pagePath": "pages/course/courseV1", "text": "课程", "iconPath": "static/tabbar/course.png", "selectedIconPath": "static/tabbar/activeCourse.png"}, {"pagePath": "pages/game/index", "text": "游戏", "iconPath": "static/tabbar/game.png", "selectedIconPath": "static/tabbar/activeGame.png"}, {"pagePath": "pages/user/user", "text": "我的", "iconPath": "static/tabbar/my.png", "selectedIconPath": "static/tabbar/activeMy.png"}]}, "uniIdRouter": {}, "condition": {"current": 0, "list": [{"name": "meeting", "path": "pages/classes/index"}]}}