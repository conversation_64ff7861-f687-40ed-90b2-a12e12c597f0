<template>
  <BaseLayout nav-title="童言童语" :content-style="{ padding: '0' }">
    <view class="children-words-layout">
      <!-- 儿童信息栏 -->
      <view class="child-info-bar">
        <view class="child-avatar">
          <image :src="getChildAvatar()" mode="aspectFill" />
        </view>
        <view class="child-details">
          <view class="child-name-row">
            <text class="child-name">{{ childInfo.title || childName }}</text>
            <up-icon v-if="getChildGender() === 1" name="/static/icon/male.png" size="28rpx" />
            <up-icon
              v-else-if="getChildGender() === 2"
              name="/static/icon/female.png"
              size="28rpx"
            />
          </view>
          <view class="class-info">{{ classInfo.title || '未知班级' }}</view>
        </view>
      </view>

      <!-- 标题输入区域 -->
      <view class="input-section">
        <input
          v-model="formData.title"
          class="title-input"
          placeholder="请输入标题"
          placeholder-style="color: #B1B3B5; font-size: 28rpx;"
        />
      </view>

      <!-- 录音转文本显示区域 -->
      <view class="content-section" v-if="formData.interaction">
        <view class="speech-bubble">
          <text class="speech-text">{{ formData.interaction }}</text>
        </view>
      </view>

      <!-- 录音按钮 -->
      <view class="record-section">
        <view class="record-button" :class="{ recording: recorderLoading }" @click="onRecordClick">
          <image
            class="microphone-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/microphones.svg"
            mode="aspectFit"
          />
        </view>
        <text class="record-tip" v-if="!recorderLoading">点击录音</text>
        <text class="record-tip recording" v-else>{{ recorderText }}</text>
      </view>

      <!-- 保存按钮 -->
      <view class="action-btn">
        <up-button
          type="primary"
          text="保存"
          color="#367CFF"
          round
          shape="circle"
          @click="onSave"
        ></up-button>
      </view>
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import useRecorder from './hook/useRecorder.js'
import { saveOrUpdate } from './api/index.js'
import { getChildrenInfo } from './api/children.js'
import { getclassDetail } from '@/api/classApi.js'

// 页面参数
const childId = ref('')
const childName = ref('')
const childAvatar = ref('')
const childGender = ref('')
const classId = ref('')
const schoolId = ref('')

// 数据状态
const childInfo = ref({})
const classInfo = ref({})

// 表单数据
const formData = ref({
  title: '',
  interaction: ''
})

// 录音相关状态
const recorderLoading = ref(false)
const recorderText = ref('语音转文字')
const recordingTime = ref(0) // 录音时长
let recordingTimer = null // 录音计时器

// 使用录音hook
const useRecorders = useRecorder(formData, 'interaction', recorderLoading, recorderText)

// 默认头像
const defaultAvatars = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
]

// 页面加载
onLoad((options) => {
  childId.value = options.childId
  childName.value = options.childName ? decodeURIComponent(options.childName) : ''
  childAvatar.value = decodeURIComponent(options.childAvatar || '')
  childGender.value = options.childGender
  classId.value = uni.getStorageSync('USER_INFO').currentClassId
  schoolId.value = options.schoolId || uni.getStorageSync('USER_INFO').currentSchoolId
})

onMounted(() => {
  initPage()
})

onUnmounted(() => {
  // 清理录音计时器
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }
})

// 获取儿童头像
const getChildAvatar = () => {
  let avatarUrl = ''

  // 优先使用从 index 页面传递的头像参数
  if (childAvatar.value) {
    avatarUrl = childAvatar.value
  }
  // 如果没有传递头像，从 childInfo 中获取
  else if (childInfo.value) {
    // 优先使用 headers 中的头像
    if (childInfo.value.headers && childInfo.value.headers.length > 0) {
      avatarUrl = childInfo.value.headers[0].uri
    }
    // 其次使用 header 字段
    else if (childInfo.value.header) {
      avatarUrl = childInfo.value.header
    }
    // 根据性别使用默认头像
    else {
      const gender = childInfo.value.sex || childGender.value
      avatarUrl = defaultAvatars[gender === 1 ? 0 : 1] // 1=男孩，2=女孩
    }
  }
  // 最后使用默认头像
  else {
    avatarUrl = defaultAvatars[childGender.value === 1 ? 0 : 1]
  }

  // 直接在图片地址后面拼接OSS处理参数
  return avatarUrl + '?x-oss-process=image/resize,m_fill,w_600'
}

// 获取儿童性别
const getChildGender = () => {
  // 优先使用从 index 页面传递的性别参数
  if (childGender.value) {
    return parseInt(childGender.value)
  }

  // 其次从 childInfo 中获取
  if (childInfo.value && childInfo.value.sex) {
    return childInfo.value.sex
  }

  // 默认返回男孩
  return 1
}

// 初始化页面
const initPage = async () => {
  await getChildInfo()
  await getClassInfo()
}

// 获取儿童信息
const getChildInfo = async () => {
  if (!childId.value) return

  try {
    const res = await getChildrenInfo(childId.value)
    if (res.status === 0) {
      childInfo.value = res.data
    }
  } catch (error) {
    console.error('获取儿童信息失败:', error)
  }
}

// 获取班级信息
const getClassInfo = async () => {
  if (!classId.value) return

  try {
    const res = await getclassDetail(classId.value)
    if (res.status === 0) {
      classInfo.value = res.data
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 录音按钮点击
const onRecordClick = () => {
  if (recorderLoading.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

// 开始录音
const startRecording = () => {
  recordingTime.value = 0
  useRecorders.startRecord()

  // 开始计时
  recordingTimer = setInterval(() => {
    recordingTime.value++
    recorderText.value = `${recordingTime.value}s后结束，再次点击结束`
  }, 1000)
}

// 停止录音
const stopRecording = () => {
  useRecorders.stopRecord()

  // 清除计时器
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }

  recordingTime.value = 0
  recorderText.value = '语音转文字'
}

// 保存数据
const onSave = async () => {
  if (!formData.value.title) {
    uni.showToast({
      title: '请输入标题',
      icon: 'none'
    })
    return
  }

  if (!formData.value.interaction) {
    uni.showToast({
      title: '请录制语音内容',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    const params = {
      title: formData.value.title,
      interaction: formData.value.interaction,
      childId: childId.value,
      classId: classId.value,
      schoolId: schoolId.value
    }

    const res = await saveOrUpdate(params)

    if (res.status === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.message || '保存失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.children-words-layout {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.child-info-bar {
  display: flex;
  align-items: center;
  padding: 30rpx 0rpx 60rpx 0rpx;

  .child-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .child-details {
    flex: 1;

    .child-name-row {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .child-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-right: 8rpx;
      }
    }

    .class-info {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.input-section {
  background: white;
  border-radius: 28rpx;
  padding: 10rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .title-input {
    height: 80rpx;
    border-radius: 12rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    &:focus {
      border-color: #367cff;
      background-color: #fff;
    }
  }
}

.content-section {
  border-radius: 28rpx;
  padding: 30rpx 60rpx;
  margin-bottom: 20rpx;

  .speech-bubble {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    min-height: 200rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .speech-text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #333333;
      word-wrap: break-word;
    }
  }

  .empty-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.record-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 200rpx;

  .record-button {
    width: 160rpx;
    height: 160rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 16rpx;

    &.recording {
      animation: pulse 1.5s infinite;
    }

    .microphone-icon {
      width: 160rpx;
      height: 160rpx;
    }
  }

  .record-tip {
    font-size: 24rpx;
    color: #666;

    &.recording {
      color: #ff4444;
      font-weight: 500;
    }
  }
}

.action-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: white;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(255, 68, 68, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
  }
}
</style>
