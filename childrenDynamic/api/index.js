import { request } from '@/common/request.js'

// 获取儿童详情
// export function getChildrenInfo(id) {
//   return request({
//     url: `/business/child/detail/${id}`,
//     method: 'GET'
//   })
// }


// 新增类型为照片，视频，音频的记录 /business/childjourney/addMedia
// Query 参数 childId  ids（上传文件后的返回文件id）
export function addMedia(data) {
  return request({
    url: `/jsapi/business/childjourney/addMedia`,
    method: 'GET',
    data
  })
}

// 获取儿童列表和当日动态数量 /business/childjourney/classChildNum
export function getChildrenDynamicList(data) {
  return request({
    url: `/jsapi/business/childjourney/classChildNum`,
    method: 'POST',
    data
  })
}
// 新增童言童语 /business/childTalk/saveOrUpdate
export function saveOrUpdate(data) {
  return request({
    url: `/jsapi/business/childTalk/saveOrUpdate`,
    method: 'POST',
    data
  })
}
