<template>
  <BaseLayout nav-title="动态记录" :content-style="{ padding: '0' }" containerClass="newBg">
    <!-- 班级信息 -->
    <view class="class-info">
      <text class="class-name">{{ classInfo.title || '加载中...' }}</text>
    </view>

    <!-- 性别分组标题 -->
    <view class="gender-headers">
      <view class="gender-header-item">
        <text class="gender-title girl-title">女生</text>
      </view>
      <view class="gender-header-item">
        <text class="gender-title boy-title">男生</text>
      </view>
    </view>

    <!-- 儿童网格 -->
    <view class="children-container">
      <view class="children-row" v-for="(row, rowIndex) in childrenRows" :key="rowIndex">
        <!-- 女生列 -->
        <view class="gender-column girls-column">
          <view
            v-for="child in row.girls"
            :key="child.id"
            class="child-item"
            @click="selectChild(child)"
          >
            <view class="child-avatar-wrapper">
              <image
                class="child-avatar girl-avatar"
                :src="getChildAvatar(child)"
                mode="aspectFill"
              />
            </view>
            <text class="child-name">{{ child.name }}</text>
            <text class="child-record">今日{{ child.recordCount || 0 }}条</text>
          </view>
        </view>

        <!-- 男生列 -->
        <view class="gender-column boys-column">
          <view
            v-for="child in row.boys"
            :key="child.id"
            class="child-item"
            @click="selectChild(child)"
          >
            <view class="child-avatar-wrapper">
              <image
                class="child-avatar boy-avatar"
                :src="getChildAvatar(child)"
                mode="aspectFill"
              />
            </view>
            <text class="child-name">{{ child.name }}</text>
            <text class="child-record">今日{{ child.recordCount || 0 }}条</text>
          </view>
        </view>
      </view>
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getChildrenList } from '@/api/children.js'
import { getclassDetail } from '@/api/classApi.js'
import { getChildrenDynamicList } from './api/index.js'
import BaseLayout from '@/components/base-layout/base-layout.vue'

// 页面参数
const classId = ref('')
const schoolId = ref('')

// 页面数据
const classInfo = ref({})
const childrenList = ref([])

// 默认头像
const defaultAvatars = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
]

// 计算属性：按性别分组
const girlsList = computed(() => {
  return childrenList.value.filter((child) => child.gender === 2)
})

const boysList = computed(() => {
  return childrenList.value.filter((child) => child.gender === 1)
})

// 计算属性：按行分组儿童数据
const childrenRows = computed(() => {
  const girls = girlsList.value
  const boys = boysList.value
  const maxLength = Math.max(girls.length, boys.length)
  const rows = []

  // 每行2个女生，2个男生
  for (let i = 0; i < maxLength; i += 2) {
    const row = {
      girls: girls.slice(i, i + 2),
      boys: boys.slice(i, i + 2)
    }
    rows.push(row)
  }

  return rows
})

// 页面加载
onLoad((options) => {
  classId.value = options.classId || uni.getStorageSync('USER_INFO').currentClassId
  schoolId.value = options.schoolId || uni.getStorageSync('USER_INFO').currentSchoolId
})

onShow(() => {
  initPage()
})

onMounted(() => {
  initPage()
})

// 初始化页面
const initPage = async () => {
  await getClassInfo()
  await getChildrenInfo()
}

// 获取班级信息
const getClassInfo = async () => {
  try {
    const res = await getclassDetail(classId.value)
    if (res.status === 0 && res.data) {
      classInfo.value = res.data
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 获取儿童列表和当日动态数量
const getChildrenInfo = async () => {
  try {
    // 获取当天日期
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const todayStr = `${year}-${month}-${day}`

    const params = {
      classId: parseInt(classId.value),
      beginDate: todayStr,
      endDate: todayStr
    }

    const res = await getChildrenDynamicList(params)
    if (res.status === 0 && res.data) {
      const children = res.data.map((childData) => {
        // 处理头像，优先使用接口返回的url，否则根据性别使用默认头像
        let avatar = childData.url || defaultAvatars[childData.sex === 1 ? 0 : 1] // 1=男孩，2=女孩

        return {
          id: childData.childId,
          name: childData.childName,
          avatar: avatar,
          gender: childData.sex,
          recordCount: childData.num || 0 // 使用接口返回的当日动态数量
        }
      })

      childrenList.value = children
    }
  } catch (error) {
    console.error('获取儿童信息失败:', error)
    // 如果获取动态列表失败，回退到获取基础儿童列表
    await getBasicChildrenInfo()
  }
}

// 获取基础儿童列表（备用方法）
const getBasicChildrenInfo = async () => {
  try {
    const params = {
      classId: classId.value,
      current: 1,
      pageSize: 999,
      state: 1 // 在园状态
    }

    const res = await getChildrenList(params)
    if (res.status === 0 && res.data) {
      const children = res.data.map((child) => {
        // 处理头像，优先使用 headers 中的头像，否则根据性别使用默认头像
        let avatar = defaultAvatars[child.sex === 1 ? 0 : 1] // 1=男孩，2=女孩
        if (child.headers && child.headers.length > 0) {
          avatar = child.headers[0].uri
        } else if (child.header) {
          avatar = child.header
        }

        return {
          id: child.id,
          name: child.title,
          avatar: avatar,
          gender: child.sex,
          recordCount: 0 // 无法获取动态数量时显示0
        }
      })

      childrenList.value = children
    }
  } catch (error) {
    console.error('获取基础儿童信息失败:', error)
  }
}

// 处理头像地址，添加阿里云OSS图片处理参数
const getChildAvatar = (child) => {
  let avatarUrl = child.avatar
  if (!avatarUrl) {
    avatarUrl = defaultAvatars[child.gender === 1 ? 0 : 1] // 1=男孩，2=女孩
  }

  // 直接在图片地址后面拼接OSS处理参数
  return avatarUrl + '?x-oss-process=image/resize,m_fill,w_600'
}

// 选择儿童
const selectChild = (child) => {
  // 跳转到动态记录页面
  uni.navigateTo({
    url: `/childrenDynamic/index?childId=${child.id}&childName=${encodeURIComponent(
      child.name
    )}&childGender=${child.gender}`
  })
}
</script>

<style lang="scss" scoped>
.class-info {
  padding: 30rpx;

  .class-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.gender-headers {
  padding: 0 20rpx;
  display: flex;
  margin-bottom: 30rpx;

  .gender-header-item {
    flex: 1;
    text-align: center;
    .gender-title {
      font-size: 30rpx;
      font-weight: 600;

      &.girl-title {
        color: rgba(252, 151, 149, 1);
      }

      &.boy-title {
        color: rgba(78, 176, 251, 1);
      }
    }
  }
}

.children-container {
  padding: 0 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.children-row {
  display: flex;
  margin-bottom: 30rpx;
  gap: 20rpx;
  width: 100%;
}

.gender-column {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  min-width: 0; /* 防止flex子项溢出 */
}

.child-item {
  border-radius: 24rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.02);
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
  min-width: 0; /* 防止溢出 */

  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .child-avatar-wrapper {
    position: relative;

    .child-avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      border: 4rpx solid transparent;

      &.girl-avatar {
        border-color: rgba(252, 151, 149, 1);
      }

      &.boy-avatar {
        border-color: rgba(78, 176, 251, 1);
      }
    }
  }

  .child-name {
    font-size: 24rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .child-record {
    font-size: 20rpx;
    color: #999;
    text-align: center;
    white-space: nowrap;
  }
}
</style>
