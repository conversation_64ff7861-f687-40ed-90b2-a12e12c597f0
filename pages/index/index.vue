<template>
  <view class="layout">
    <view class="copyUrl" @tap="onCopyUrl">
      <image src="@/static/icon/index_web_3x.png" />
      <view class="copyUrl-text">{{ WEB_URL }}</view>
      <image src="@/static/icon/index_copy_3x.png" />
    </view>
    <view class="col gridcol">
      <up-grid :border="true" :col="2" align="center">
        <up-grid-item
          :customStyle="{ width: '100%', height: 127 + 'rpx' }"
          v-for="(item, index) in swiperList"
          :index="index"
          :key="index"
          @click="gotoItem(item)"
        >
          <view class="gridcol-item">
            <image
              :src="item.url"
              v-if="item.text === '记录幼儿动态'"
              style="width: 80rpx; height: 80rpx; margin-right: 20rpx"
            />
            <image :src="item.url" v-else />
            <text class="grid-text">{{ item.text }}</text>
            <view v-if="item.isFree" class="free">限免</view>
          </view>
        </up-grid-item>
      </up-grid>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <view style="margin-top: 20rpx">
      <official-account></official-account>
    </view>
    <!-- #endif -->
    <view class="col col-2">
      <view class="col-2-title">
        <text class="col-2-title-left">青禾AI—文案生成工具</text>
        <view class="col-2-title-right" @click="gotoAiTemplateList">
          <text>查看全部</text>
          <up-icon name="arrow-right" color="#808080" size="22rpx"></up-icon>
        </view>
      </view>
      <up-grid :border="false" :col="3" align="center">
        <up-grid-item
          v-for="(baseListItem, baseListIndex) in baseList"
          :key="baseListIndex"
          @click="openAiTemplatePage(baseListItem.id, baseListItem.name)"
        >
          <image
            class="template-logo"
            :src="baseListItem.templateImageUrl"
            mode="widthFix"
            v-if="!!baseListItem.templateImageUrl"
          ></image>
          <image
            v-else
            class="template-logo"
            src="@/static/icon/defaultTemplateIcon.svg"
            mode="widthFix"
          />
          <text class="template-text">{{ baseListItem.name }}</text>
        </up-grid-item>
      </up-grid>
    </view>

    <!-- 悬浮按钮 -->
    <view class="float-button" @click="isFloat = !isFloat">
      <image v-if="!isFloat" src="/static/icon/index-5.png" style="width: 48rpx; height: 47rpx" />
      <image v-else src="/static/icon/index-4.png" style="width: 52rpx; height: 52rpx" />
      <view
        class="float-item"
        v-for="(item, index) in floatList"
        :key="index"
        :class="{ [`item${index + 1}`]: isFloat }"
        :style="{ visibility: isFloat ? 'visible' : 'hidden' }"
        @click.prevent="floatGoto(item)"
      >
        <image :src="item.url" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { checks, sharePageObj, headleLogger, getDICT } from '@/utils/index.js'
import { getAiTemplateListNew } from '@/api/aiTemplate.js'
import { getUserInfo } from '@/api/login.js'
import config from '@/common/config.js'

let isFloat = ref(false) // 悬浮按钮
const WEB_URL = config[config.DEFINE_ENV].WEB_URL
const swiperList = reactive([
  // {
  //   text: "观察记录",
  //   url: "https://c.mypacelab.com/vxmp/img/index_grid1.png",
  //   route: "/observation/observationRecord",
  //   isFree: false,
  // },
  {
    text: '周报',
    url: 'https://c.mypacelab.com/vxmp/img/index_grid1.png',
    route: '/weeklyReport/index',
    isFree: false
  },
  {
    text: '核心经验评价',
    url: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_grid2.png',
    route: '/childCoreAssessment/index/index',
    isFree: false
  },
  {
    text: '会议管理',
    url: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_grid3.png',
    route: '/subPages/meeting/index',
    isFree: true
  },
  {
    text: '师幼互动评价',
    url: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_grid4.png',
    route: '/subPages/meeting/teacherChildInteraction/index',
    // route: "",
    isFree: false
  },
  {
    text: '全园出勤统计',
    url: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/cqtj.svg',
    route: '/childrenAttendance/attendanceRecordDetail?type=classSummary',
    isFree: false
  },
  {
    text: '记录幼儿动态',
    url: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/yrdt.svg',
    route: '/childrenDynamic/select',
    isFree: false
  }
])
// 悬浮列表数据
const floatList = [
  {
    url: '/static/icon/index-3.png',
    name: '反馈列表',
    path: '/feedback/index/index'
  },
  {
    url: '/static/icon/index-2.png',
    name: '新增反馈',
    path: '/feedback/addfeedback/addfeedback'
  },
  {
    url: '/static/icon/index-1.png',
    name: '联系客服',
    path: 'https://work.weixin.qq.com/kfid/kfc33433ad794129a42'
  }
]

// 创建响应式数据
const baseList = ref([])

// 复制网址
const onCopyUrl = () => {
  uni.setClipboardData({
    data: WEB_URL,
    success: () => {
      uni.showToast({
        title: '复制成功，请在浏览器打开',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

const getBaseList = async () => {
  const res = await getAiTemplateListNew({
    pageSize: 9,
    currentPage: 1,
    pageModel: {
      keyword: ''
    }
  })
  baseList.value = res.data
}

const gotoAiTemplateList = () => {
  uni.navigateTo({
    url: '/subPages/aiAssistant/aiTemplateList/index'
  })
}

const gotoItem = (item) => {
  // 如果是记录幼儿动态，需要检查是否已选择学校和班级
  if (item.text === '记录幼儿动态') {
    const userInfo = uni.getStorageSync('USER_INFO')
    const currentSchoolId = userInfo?.currentSchoolId
    const currentClassId = userInfo?.currentClassId

    // 检查是否已选择学校和班级
    if (!currentSchoolId || !currentClassId) {
      uni.showToast({
        title: '请前往 我的 中切换班级',
        icon: 'none',
        duration: 2000
      })
      return
    }
  }

  if (item.route) {
    uni.navigateTo({
      url: item.route
    })
  } else {
    uni.$u.toast('敬请期待')
  }
}
// 悬浮按钮点击事件
const floatGoto = (item) => {
  console.log(item)
  const { path, name } = item
  if (name == '联系客服') {
    // #ifdef MP-WEIXIN
    wx.openCustomerServiceChat({
      extInfo: { url: path },
      corpId: 'ww1da4260b4b85c0bd'
    })
    // #endif
    // #ifdef H5
    window.open(path)
    // #endif
  }
  uni.navigateTo({
    url: path
  })
}

// 小程序分享页面
onShareAppMessage(() => sharePageObj())

const openAiTemplatePage = (id, title) => {
  uni.navigateTo({
    url: `/subPages/aiAssistant/aiTemplate/index?id=${id}&title=${title}`,
    fail: () => {
      uni.switchTab({
        url: '/subPages/aiAssistant/aiTemplateList/index'
      })
    }
  })
}
onMounted(() => {
  getDICT()
  // 如果没有有当前班级Id，提示用户去我的页面选择班级
  let curClassId = uni.getStorageSync('USER_INFO')?.currentClassId

  if (typeof curClassId != 'number') {
    getUserInfo().then((res) => {
      console.log(res, 'me')
      const { data } = res

      // 确保存储完整的用户信息，包括用户ID
      const userInfo = {
        id: data.id,
        nickname: data.nickname,
        name: data.name,
        mobile: data.mobile,
        post: data.post,
        note: data.note,
        isTeacher: data.isTeacher,
        currentClassId: data.currentClassId,
        classIds: data.classIds,
        currentSchoolId: data.currentSchoolId,
        currentTerm: data.currentTerm,
        header: data.header
      }

      // 如果没有默认班级，设置第一个班级为默认班级
      if (!data.currentClassId && data.classIds && data.classIds.length > 0) {
        userInfo.currentClassId = data.classIds[0]
      }

      uni.setStorageSync('USER_INFO', userInfo)
    })
  }
})

// 解决在微信小程序端点击tabbar的底层逻辑并不是触发uni.switchTab。所以误认为拦截无效，此类场景的解决方案是在tabbar页面的页面生命周期onShow中处理。
onShow(() => {
  checks()
  getBaseList()
  // #ifdef MP-WEIXIN
  headleLogger('首页')
  // #endif
})
</script>
<script>
export default {
  options: { styleIsolation: 'shared' } // 解除样式隔离
}
</script>

<style lang="scss" scoped>
.layout {
  box-sizing: border-box;
  background: #f5f5f5 url('https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_bg.png')
    no-repeat top;
  background-size: 100% auto;
  height: 100%;
  overflow-y: auto;
  padding: 0 32rpx;
  position: relative;
  .copyUrl {
    width: fit-content;
    position: absolute;
    top: 278rpx;
    left: 66rpx;
    font-size: 24rpx;
    font-weight: 400;
    color: #000000;
    display: flex;
    align-items: center;
    gap: 8rpx;
    &-text {
      margin-bottom: 3rpx;
    }
    image {
      width: 24rpx;
      height: 25rpx;
    }
    image:last-child {
      margin-top: 3rpx;
    }
  }
}

.col {
  width: 100%;
  border-radius: 28rpx;
  background: #fff;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  padding: 28rpx;
  box-sizing: border-box;
}

.col-2 {
  margin-top: 26rpx;
}

.col-2-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.col-2-title-left {
  font-size: 30rpx;
  font-weight: 600;
}

.col-2-title-right {
  display: flex;
  align-items: baseline;
  font-size: 22rpx;
  font-weight: 400;
  color: #808080;
}

.swiper {
  height: 305rpx;
}

.gridcol {
  margin-top: 409rpx;
  height: 460rpx;
  padding: 40rpx 32rpx;
}

.gridcol-item {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative;
}

.grid-text {
  font-size: 28rpx;
  font-weight: 600;
}

.free {
  position: absolute;
  top: 24rpx;
  right: 14rpx;
  width: 56rpx;
  height: 30rpx;
  font-size: 18rpx;
  font-weight: 500;
  line-height: 30rpx;
  text-align: center;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(241, 209, 150, 1) 0%,
    rgba(253, 230, 180, 1) 55.14%,
    rgba(226, 191, 122, 1) 100%
  );
}

.gridcol-item image {
  width: 90rpx;
  height: 90rpx;
  margin: 0 16rpx 0 28rpx;
}

.float-button {
  width: 96rpx;
  height: 96rpx;
  background: rgba(54, 124, 255, 1);
  box-shadow: 6rpx 12rpx 20rpx rgba(63, 121, 255, 0.16);
  border-radius: 50%;
  position: fixed;
  right: 32rpx;
  bottom: 160rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.float-item {
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border: 1rpx solid #eee;
  box-shadow: 8rpx 16rpx 32rpx rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  position: absolute;
  transition: all 0.5s ease;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.float-item image {
  width: 36rpx;
  height: 36rpx;
}

.item1 {
  top: 50%;
  left: -140rpx;
  transform: translate(0, -50%);
}

.item2 {
  top: -96rpx;
  left: -96rpx;
}

.item3 {
  top: -140rpx;
  left: 50%;
  transform: translate(-50%, 0);
}

.template-logo {
  width: 48rpx;
  height: 48rpx;
  padding: 16rpx;
}

.template-text {
  font-size: 22rpx;
  font-weight: 400;
  color: #808080;
  width: 100%;
  display: block;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  display: -webkit-box;
}

/* #ifdef MP-WEIXIN */
.float-button {
  bottom: 60rpx;
}

/* #endif */
</style>
