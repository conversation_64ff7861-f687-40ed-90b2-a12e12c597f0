<template>
  <BaseLayout2
    :scrollEnabled="false"
    nav-title="游戏"
    :content-style="{ padding: '0' }"
    :footerStyle="{ display: 'none' }"
  >
    <view class="container">
      <view class="class-bar" @click="isClassShow = true">
        <text class="class-title">{{ currentTitle || '-' }}</text>
        <image class="change" src="/static/game/change.svg" />
      </view>
      <view class="row">
        <view class="left_box">
          <view class="left_box_top" @click="jumpTo(0)">
            <view class="title-box">
              <view class="title">幼儿进区</view>
              <view class="btn">查看</view>
            </view>
            <view class="value">今日 {{ todayChildrenCount }} 人次</view>
            <view class="value">已记录 {{ entryDayCount }} 天</view>
          </view>
          <view class="left_box_bottom" @click="jumpTo(1)">
            <image
              class="add-icon"
              style="margin-left: 0; margin-right: 30rpx"
              src="/static/game/kscj.svg"
            />
            <view class="add-box">
              <view class="title">开始选区</view>
              <view class="value">新增幼儿进区</view>
            </view>
          </view>
        </view>
        <view class="right_box">
          <view class="top_box" @click="jumpTo(2)">
            <view class="add-box">
              <view class="title">观察记录</view>
              <view class="value">共计 {{ observationCount }} 份</view>
            </view>
            <image
              class="add-icon add-icon-mr"
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_add.svg"
              style="margin-left: 10rpx"
            />
          </view>
          <view class="bottom_box" @click="jumpTo(3)">
            <view class="add-box">
              <view class="title">表征作品/倾听</view>
              <view class="value">共计 {{ listeningRecordCount }} 份</view>
            </view>
            <image
              class="add-icon add-icon-mr"
              style="margin-left: 10rpx"
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_add.svg"
            />
          </view>
        </view>
      </view>
      <view class="row row_2">
        <view class="box" @click="jumpTo(4)">
          <view class="add-box">
            <view class="title">班级区域</view>
            <view class="value">共 {{ areaTotal }} 个</view>
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_bjqy.svg"
          />
        </view>
        <view class="box" @click="jumpTo(5)">
          <view class="add-box">
            <view class="title">班级材料</view>
            <view class="value">共 {{ materialTotal }} 个</view>
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_bjcl.svg?a=1"
          />
        </view>
      </view>
      <view class="row row_2">
        <view class="box" @click="jumpTo(6)">
          <view class="add-box">
            <view class="title" style="display: flex; flex-direction: column">
              <text>材料核心</text>
              <text>经验统计</text>
            </view>
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_tjfx.svg"
            style="width: 62rpx; height: 60rpx; margin-right: 10rpx"
          />
        </view>
        <view class="box" @click="jumpTo(7)">
          <view class="add-box">
            <view class="title">AI材料报告</view>
            <view class="value">共 {{ materialReportCount }} 个</view>
          </view>
          <image
            class="add-icon"
            style="width: 75rpx; height: 73rpx"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/clbg.svg"
          />
        </view>
      </view>
      <view class="row row_2">
        <view class="box" @click="jumpTo(8)">
          <view class="add-box">
            <view class="title">户外区域</view>
            <view class="value">共 {{ outsideAreaTotal }} 个</view>
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_hyqy.svg"
          />
        </view>
        <view class="box" @click="jumpTo(9)">
          <view class="add-box">
            <view class="title">户外材料</view>
            <view class="value">共 {{ outsideMaterialTotal }} 个</view>
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_hycl.svg"
          />
        </view>
      </view>
      <view class="row row_2">
        <view class="box" @click="jumpTo(10)">
          <view class="add-box">
            <view class="title">材料推荐</view>
            <!-- <view class="value">共 {{ outsideMaterialTotal }} 个</view> -->
          </view>
          <image
            class="add-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_hycl.svg"
          />
          <image
            class="new-icon"
            src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/icon/new.svg"
          />
        </view>
      </view>
      <up-picker
        ref="uPicker"
        :show="isClassShow"
        :columns="columns"
        :defaultIndex="[0, 0]"
        keyName="title"
        :loading="pickerLoading"
        @confirm="classConfirm"
        @change="classChange"
        @cancel="isClassShow = false"
      />
    </view>
  </BaseLayout2>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { onShow, onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import { checks, sharePageObj, useCurrentClassId, headleLogger } from '@/utils/index.js'
import { getclassList, getSchoolList } from '@/api'
import BaseLayout2 from '@/components/base-layout/base-layout2.vue'
import {
  listByClass,
  listOutSideArea,
  getMaterialNumByClassId,
  listArea,
  listOneToOneListeningRecord,
  getObservationList,
  getMaterialNumBySchool,
  countClassEntryDay,
  getMaterialReportNumByClass
} from '@/api/game.js'
let currentTitle = ref('') // 当前班级名称
let currentClassId = ref('') // 当前班级id
let currentSchoolId = ref('') // 当前学校id
let currentSchoolName = ref('') // 当前学校名称
let isClassShow = ref(false)
let columns = reactive([[]])
let uPicker = ref(null)
let pickerLoading = ref(false)
//区域管理总数
let areaTotal = ref(0)
//班级材料总数
let materialTotal = ref(0)
//今日进区人次
let todayChildrenCount = ref(0)
//表征作品/倾听总数
let listeningRecordCount = ref(0)
//观察记录总数
let observationCount = ref(0)
//户外区域总数
let outsideAreaTotal = ref(0)
// 户外材料总数
let outsideMaterialTotal = ref(0)
// 进区天数
let entryDayCount = ref(0)
// AI 分析报告
let materialReportCount = ref(0)
// 数据加载状态
let isDataLoading = ref(false)
const classConfirm = async (e) => {
  const { value } = e
  await useCurrentClassId(value[1].id)
  currentTitle.value = value[1].title
  currentClassId.value = value[1].id
  isClassShow.value = false

  // 班级切换后刷新所有数据
  isDataLoading.value = true
  try {
    await refreshAllData()
  } catch (error) {
    console.error('刷新数据失败:', error)
    uni.showToast({
      title: '数据加载失败，请重试',
      icon: 'none'
    })
  } finally {
    isDataLoading.value = false
  }
}
const classChange = async (e) => {
  const { columnIndex, value } = e
  if (columnIndex === 0) {
    pickerLoading.value = true
    await useCurrentClassId(null, value[columnIndex].id)
    // 更新学校名称，确保schoolId和schoolName一致
    currentSchoolId.value = value[columnIndex].id
    currentSchoolName.value = value[columnIndex].title
    // 同步更新到本地存储
    uni.setStorageSync('CURRENT_SCHOOL_TITLE', value[columnIndex].title)
    columns.pop()
    await classList(value[columnIndex].id)
    pickerLoading.value = false
  }
}
const classList = async (schoolId) => {
  try {
    let userInfo = uni.getStorageSync('USER_INFO')
    currentClassId.value = userInfo.currentClassId
    const data = { schoolId }
    const fn = schoolId ? getclassList(data) : getclassList()
    const res = await fn
    const list = res.data
    if (res.status == 0) {
      currentTitle.value = list.find((item) => item.id == currentClassId.value)?.title
      // 确保只有两列：学校列和班级列
      if (columns.length > 1) {
        columns.splice(1) // 删除第二列及之后的所有列
      }
      columns.push(list)
    }
  } catch (error) {
    console.error('获取班级列表失败', error)
    uni.showToast({ title: '获取班级列表失败', icon: 'none' })
  }
}
const schoolList = async () => {
  try {
    columns.length = 0
    let userInfo = uni.getStorageSync('USER_INFO')
    currentSchoolId.value = userInfo.currentSchoolId
    const res = await getSchoolList()
    if (res.status == 0) {
      columns[0] = res.data
      let val = res.data.find((item) => item.id == userInfo.currentSchoolId)
      let index = res.data.findIndex((item) => item.id == userInfo.currentSchoolId)
      columns[0].splice(index, 1)
      columns[0].unshift(val)
      // 设置当前学校名称，确保schoolId和schoolName匹配
      if (val) {
        currentSchoolName.value = val.title
        // 同步更新到本地存储，确保一致性
        uni.setStorageSync('CURRENT_SCHOOL_TITLE', val.title)
      }
      await classList()
    }
  } catch (error) {
    console.error('获取学校列表失败', error)
    uni.showToast({ title: '获取学校列表失败', icon: 'none' })
  }
}
const jumpTo = (type) => {
  switch (type) {
    case 0:
      uni.navigateTo({
        url:
          '/childrenAnalysis/index' +
          '?className=' +
          currentTitle.value +
          '&classId=' +
          currentClassId.value
      })
      break
    case 1:
      uni.navigateTo({
        url:
          '/childrenInArea/index' +
          '?className=' +
          currentTitle.value +
          '&classId=' +
          currentClassId.value
      })
      break
    case 2:
      uni.navigateTo({
        url: '/observation/observationRecord'
      })
      break
    case 3:
      uni.navigateTo({ url: '/oneToOneListening/index' })
      break
    case 4:
      uni.navigateTo({
        url:
          '/regionalManagement/index' +
          '?className=' +
          currentTitle.value +
          '&classId=' +
          currentClassId.value
      })
      break
    case 5:
      uni.navigateTo({ url: '/classMaterials/index' })
      break
    case 6:
      uni.navigateTo({ url: '/empiricalStatistics/index?className=' + currentTitle.value })
      break
    case 7:
      if (materialTotal.value < 50) {
        uni.showToast({
          title: 'AI分析报告需要50个以上材料才能生成',
          icon: 'none'
        })
        return
      }
      uni.navigateTo({
        url: `/AImaterialReport/index?classId=${currentClassId.value}&className=${currentTitle.value}&schoolId=${currentSchoolId.value}&schoolName=${currentSchoolName.value}`
      })
      break
    case 8:
      uni.navigateTo({
        url:
          '/regionalManagement/index' +
          '?className=' +
          currentTitle.value +
          '&classId=' +
          currentClassId.value +
          '&isOutside=1'
      })
      break
    case 9:
      const userInfo = uni.getStorageSync('USER_INFO')
      userInfo['isOutside'] = 1
      uni.setStorageSync('USER_INFO', userInfo)
      uni.navigateTo({ url: `/classMaterials/index` })
      break
    case 10:
      uni.navigateTo({ url: `/classMaterials/recommend/index` })
      break

    default:
      uni.showToast({
        title: '敬请期待',
        icon: 'none'
      })
  }
}
// 获取区域总数
const fetchAreaTotal = async (classId) => {
  const res = await listByClass({
    classId
  })
  if (res.status == 0) {
    areaTotal.value = res.data.total
  }
}
// 获取户外区域总数
const fetchOutsideAreaTotal = async (schoolId) => {
  const res = await listOutSideArea({
    schoolId
  })
  if (res.status == 0) {
    outsideAreaTotal.value = res.data.total
  }
}
// 获取班级材料总数
const fetchMaterialTotal = async (classId) => {
  const res = await getMaterialNumByClassId({
    classId
  })
  if (res.status == 0) {
    materialTotal.value = res.data
  }
}
// 获取今日进区人次
const fetchTodayChildrenCount = async (classId) => {
  try {
    const areaRes = await listArea({
      classId
    })
    if (areaRes.status == 0) {
      // 排除区域名为"未进区"的区域，并累计所有区域的childCount作为今日进区总人次
      todayChildrenCount.value = areaRes.data
        .filter((area) => area.area !== '未进区')
        .reduce((sum, area) => sum + (area.childCount || 0), 0)
    } else {
      todayChildrenCount.value = 0
      console.error('获取区域列表失败', areaRes)
    }
  } catch (error) {
    todayChildrenCount.value = 0
    console.error('获取今日进区人次失败', error)
  }
}
// 获取表征作品/倾听记录总数
const fetchListeningRecordCount = async (classId) => {
  // 获取表征作品/倾听记录总数
  try {
    const listeningRes = await listOneToOneListeningRecord({
      pageSize: 10,
      current: 1,
      schoolClassId: classId
    })
    if (listeningRes.status === 0 && listeningRes.data) {
      listeningRecordCount.value = listeningRes.data.length
    } else {
      listeningRecordCount.value = 0
      console.error('获取表征作品/倾听记录失败', listeningRes)
    }
  } catch (error) {
    listeningRecordCount.value = 0
    console.error('获取表征作品/倾听记录失败', error)
  }
}
// 获取观察记录总数
const fetchObservationCount = async (classId) => {
  // 获取观察记录总数
  try {
    const observationRes = await getObservationList({
      pageSize: 10, // 只需要获取总数，页大小可以设小一点
      current: 1,
      pageModel: {
        classId,
        schoolClassId: classId
      }
    })
    if (observationRes.status === 0 && observationRes.metadata) {
      observationCount.value = observationRes.metadata.count
    } else {
      observationCount.value = 0
      console.error('获取观察记录失败', observationRes)
    }
  } catch (error) {
    observationCount.value = 0
    console.error('获取观察记录失败', error)
  }
}
// 获取户外材料总数
const fetchOutsideMaterialTotal = async (schoolId) => {
  const res = await getMaterialNumBySchool({
    schoolId
  })
  if (res.status == 0) {
    outsideMaterialTotal.value = res.data
  }
}
// 获取进区天数
const fetchEntryDayCount = async (classId) => {
  const res = await countClassEntryDay({
    classId
  })
  if (res.status == 0) {
    entryDayCount.value = res.data
  }
}
// 获取AI分析报告数
const fetchMaterialReportCount = async (classId) => {
  const res = await getMaterialReportNumByClass({
    classId
  })
  if (res.status == 0) {
    materialReportCount.value = res.data
  }
}

// 统一刷新所有数据的函数
const refreshAllData = async () => {
  const userInfo = uni.getStorageSync('USER_INFO')
  const classId = userInfo.currentClassId

  // 并行调用所有接口，提高性能
  try {
    await Promise.all([
      fetchAreaTotal(classId),
      fetchMaterialTotal(classId),
      fetchTodayChildrenCount(classId),
      fetchListeningRecordCount(classId),
      fetchObservationCount(classId),
      fetchOutsideMaterialTotal(userInfo.currentSchoolId),
      fetchOutsideAreaTotal(userInfo.currentSchoolId),
      fetchEntryDayCount(classId),
      fetchMaterialReportCount(classId)
    ])
  } catch (error) {
    console.error('获取数据失败:', error)
    throw error
  }
}

onShow(async () => {
  checks()
  // #ifdef MP-WEIXIN
  headleLogger('游戏')
  // #endif
  let userInfo = uni.getStorageSync('USER_INFO')
  const classId = userInfo.currentClassId

  // 如果没有班级选择器数据或者当前班级ID发生变化，重新初始化
  if (!currentTitle.value || currentClassId.value !== classId) {
    await schoolList()
  }

  // 更新当前班级ID
  currentClassId.value = classId

  // 确保schoolId和schoolName同步
  if (!currentSchoolName.value && currentSchoolId.value) {
    const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
    if (savedSchoolTitle) {
      currentSchoolName.value = savedSchoolTitle
    } else {
      // 如果没有缓存的学校名称，重新获取
      await schoolList()
    }
  }

  if (userInfo.isOutside) {
    userInfo.isOutside = 0
    uni.setStorageSync('USER_INFO', userInfo)
  }

  // 统一刷新所有数据
  isDataLoading.value = true
  try {
    await refreshAllData()
  } catch (error) {
    console.error('页面数据加载失败:', error)
  } finally {
    isDataLoading.value = false
  }
})

onLoad(async () => {})
onShareAppMessage(() => sharePageObj())
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 0 32rpx;
}

.class-bar {
  width: 174rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background: rgba(255, 255, 255, 0.6);
  border: 1rpx solid rgba(227, 230, 235, 1);

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  margin: 32rpx 0 24rpx 0;
  color: rgba(51, 51, 51, 1);

  .change {
    width: 32rpx;
    height: 32rpx;
    margin-left: 8rpx;
  }
}

.class-title {
  font-size: 28rpx;
  font-weight: 500;
}

.row {
  display: flex;
  justify-content: space-between;

  &.row_2 {
    margin-top: 20rpx;
  }
}

// 基础卡片样式
.card-base {
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid rgba(255, 255, 255, 1);
}

// 通用文本样式
.text-title {
  font-size: 30rpx;
  font-weight: 600;
}

.text-value {
  font-size: 24rpx;
  color: rgba(128, 128, 128, 1);
}

// 通用图标样式
.add-icon {
  margin-left: 30rpx;
}
// 单独设置右外边距为0
.add-icon-mr {
  margin-right: 0 !important;
}

// 左侧大卡片
.left_box {
  width: 330rpx;
  height: 324rpx;
  @extend .card-base;
  align-items: flex-start;
  background: linear-gradient(136.19deg, rgba(109, 195, 252, 1) 0%, rgba(64, 131, 255, 1) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  flex-direction: column;
  justify-content: space-between;

  .left_box_top {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-left: 30rpx;
    justify-content: center;

    .title-box {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
    }
    .title {
      @extend .text-title;
      color: rgba(255, 255, 255, 1);
      margin-right: 20rpx;
    }
    .btn {
      width: 80rpx;
      height: 40rpx;
      line-height: 40rpx;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 18rpx;
      color: rgba(255, 255, 255, 1);
      font-size: 24rpx;
      text-align: center;
    }

    .value {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .left_box_bottom {
    width: 100%;
    height: 148rpx;
    border-radius: 28rpx;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 30rpx;

    .add-icon {
      width: 80rpx;
      height: 80rpx;
      @extend .add-icon;
    }

    .add-box {
      display: flex;
      flex-direction: column;

      .title {
        font-size: 26rpx;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
        margin-bottom: 10rpx;
      }

      .value {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

// 右侧卡片容器
.right_box {
  width: 330rpx;
  height: 324rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  // 右侧小卡片通用样式
  .top_box,
  .bottom_box {
    width: 100%;
    height: 148rpx;
    @extend .card-base;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 30rpx;

    .add-icon {
      width: 60rpx;
      height: 60rpx;
    }

    .add-box {
      display: flex;
      flex-direction: column;

      .title {
        @extend .text-title;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 10rpx;
      }

      .value {
        @extend .text-value;
      }
    }
  }

  .top_box {
    background: linear-gradient(0deg, rgba(255, 232, 232, 1), rgba(255, 232, 232, 1)),
      linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  }

  .bottom_box {
    background: linear-gradient(0deg, rgba(220, 252, 249, 1), rgba(220, 252, 249, 1)),
      linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  }
}

// 第二行卡片
.row_2 .box {
  width: 330rpx;
  height: 140rpx;
  @extend .card-base;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.02);
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 30rpx;
  position: relative;

  .add-icon {
    width: 72rpx;
    height: 70rpx;
  }
  .new-icon {
    position: absolute;
    right: -10rpx;
    top: -25rpx;
    width: 72rpx;
    height: 70rpx;
  }

  .add-box {
    flex: 1;
    display: flex;
    flex-direction: column;

    .title {
      @extend .text-title;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 10rpx;
    }

    .value {
      @extend .text-value;
    }
  }
}

::v-deep .u-navbar__content__left {
  display: none !important;
}
</style>
