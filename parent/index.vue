<template>
  <view class="custom-layout">
    <!-- 顶部导航栏 -->
    <up-navbar
      title="家长管理"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    >
    </up-navbar>

    <view class="main-container">
      <!-- 家长列表 -->
      <view class="parent-list">
        <scroll-view scroll-y class="parent-scroll" @scrolltolower="scrolltolower">
          <view class="parent-container">
            <view v-if="isLoading">
              <view v-for="(item, index) in listdata.data" :key="item.id" class="user-item">
                <view class="user-avatar">
                  <image :src="getParentAvatar(item)" />
                </view>
                <view class="user-info">
                  <view class="user-header">
                    <text class="user-name">{{ item.name }}</text>
                    <text class="user-note">{{ item.note }}</text>
                  </view>
                  <view class="user-relation">
                    {{ item.childRelationshipText }}
                  </view>
                </view>
                <view class="user-action">
                  <up-icon
                    name="more-dot-fill"
                    size="20"
                    color="#C0C4CC"
                    @click="showEditOptions(item)"
                  />
                </view>
              </view>

              <!-- 加载更多状态 -->
              <view v-if="loadingMore" class="loading-more">
                <up-loading-icon mode="spinner" size="24"></up-loading-icon>
                <text class="loading-text">加载中...</text>
              </view>

              <!-- 没有更多了 -->
              <view v-if="!isRequest && listdata.data.length > 0" class="no-more">
                <text class="no-more-text">没有更多了</text>
              </view>
            </view>
            <up-loading-icon v-else mode="circle" style="margin-top: 200rpx" />
            <up-empty
              v-if="isLoading && (!listdata.data || listdata.data.length === 0)"
              mode="data"
              style="margin-top: 200rpx"
            />
          </view>
        </scroll-view>
      </view>
      <!-- 底部按钮 -->
      <view class="action-btn">
        <up-button
          type="primary"
          text="新增家长"
          color="#367CFF"
          round
          shape="circle"
          @click="addNewParent"
        />
      </view>
    </view>
    <add-form
      ref="addFormRef"
      @confirm="onConfirmUpdateParent"
      :title="'家长详情'"
      :parentItemData="listdata.parentItemData"
      :disabled="false"
    />

    <!-- 儿童选择弹窗 -->
    <up-popup :show="showChildSelector" mode="bottom" round="20" @close="cancelSelectChild">
      <view class="child-selector">
        <view class="selector-header">
          <text class="selector-title">选择要添加家长的儿童</text>
          <up-icon name="close" size="20" @click="cancelSelectChild" />
        </view>

        <scroll-view class="child-list" scroll-y>
          <view
            v-for="child in childrenList"
            :key="child.id"
            class="child-item"
            @click="selectChild(child)"
          >
            <view class="child-avatar">
              <image
                :src="
                  child.sex === 1
                    ? 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
                    : 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
                "
              />
            </view>
            <view class="child-info">
              <text class="child-name">{{ child.title || child.name }}</text>
              <text class="child-gender">{{ child.sex === 1 ? '男' : '女' }}</text>
            </view>
            <up-icon name="arrow-right" size="16" color="#C0C4CC" />
          </view>
        </scroll-view>
      </view>
    </up-popup>

    <!-- 编辑操作弹窗 -->
    <Popup :show="showEditPopup" @close="closeEditPopup">
      <view class="pop-item" @click="deleteParent">
        <up-icon name="trash" color="rgba(51, 51, 51, 1)" size="16"></up-icon>
        <text class="act">删除</text>
      </view>
      <u-gap height="20"></u-gap>
    </Popup>

    <!-- 删除确认弹窗 -->
    <up-modal
      :show="showDeleteModal"
      title="温馨提示"
      showCancelButton
      @cancel="showDeleteModal = false"
      @confirm="confirmDelete"
      :asyncClose="true"
    >
      <view class="modlCont">您是否确定删除该家长?</view>
    </up-modal>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { getParentList } from '@/api/api.js'
import { deleteParentDetail } from './api/index.js'
import { getChildrenList } from '@/api/children.js'
import { onShow, onShareAppMessage, onLoad } from '@dcloudio/uni-app'
import { checks, sharePageObj } from '@/utils/index.js'
import addForm from './components/updateParentForm.vue'
import Popup from '@/components/Popup/Popup.vue'

const listdata = reactive({
  data: [],
  parentItemData: {},
  parentDetail: {}
})
const isLoading = ref(false)
const loadingMore = ref(false)
const addFormRef = ref()
const paging = reactive({
  currentPage: 1,
  pageSize: 10
})
const isRequest = ref(true)
const currentClassId = ref('')
const childrenList = ref([])
const childrenMap = ref(new Map())
const showChildSelector = ref(false)
const selectedChild = ref(null)
const showEditPopup = ref(false)
const currentEditParent = ref(null)
const showDeleteModal = ref(false)

// 触底增加数据
async function scrolltolower() {
  if (isRequest.value && !loadingMore.value) {
    loadingMore.value = true
    paging.currentPage++

    try {
      let res = await getParentList({
        ...paging
      })
      let i = Math.ceil(res.metadata.count / paging.pageSize)
      console.log(i, '123')

      if (paging.currentPage <= i) {
        res.data.forEach((item) => {
          listdata.data.push(item)
        })
      } else {
        isRequest.value = false
      }
    } catch (error) {
      console.error('加载更多失败:', error)
      paging.currentPage-- // 回退页码
    } finally {
      loadingMore.value = false
    }
  }
}

function addNewParent() {
  // 检查是否有儿童数据
  if (!childrenList.value || childrenList.value.length === 0) {
    uni.showToast({
      title: '当前班级暂无儿童',
      icon: 'none'
    })
    return
  }

  // 显示儿童选择弹窗
  showChildSelector.value = true
}

// 选择儿童
function selectChild(child) {
  selectedChild.value = child
  showChildSelector.value = false

  // 新增模式：为选中的儿童添加家长
  listdata.parentItemData = {}
  if (addFormRef.value) {
    addFormRef.value.onStartView({ childId: child.id })
  }
}

// 取消选择儿童
function cancelSelectChild() {
  showChildSelector.value = false
  selectedChild.value = null
}

// 显示编辑选项
function showEditOptions(parent) {
  currentEditParent.value = parent
  showEditPopup.value = true
}

// 关闭编辑弹窗
function closeEditPopup() {
  showEditPopup.value = false
  currentEditParent.value = null
}

// 删除家长
function deleteParent() {
  showEditPopup.value = false
  showDeleteModal.value = true
}

// 确认删除
async function confirmDelete() {
  try {
    uni.showLoading({ title: '删除中...' })

    await deleteParentDetail(currentEditParent.value.id)

    // 从列表中移除已删除的家长
    listdata.data = listdata.data.filter((item) => item.id !== currentEditParent.value.id)

    uni.hideLoading()
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })

    showDeleteModal.value = false
    currentEditParent.value = null
  } catch (error) {
    console.error('删除家长失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
    showDeleteModal.value = false
  }
}

// 获取家长头像
function getParentAvatar(parent) {
  // 如果有gender字段，根据性别显示对应头像
  // gender: 1=男性, 2=女性，其他或没有字段默认为男性
  if (parent.gender === 2) {
    return 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
  } else {
    // 默认男性头像（包括gender为1或没有gender字段的情况）
    return 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
  }
}

// 获取儿童列表
const getChildrenData = async () => {
  try {
    const res = await getChildrenList({
      classId: currentClassId.value,
      current: 1,
      pageSize: 999,
      state: 1 // 在园状态
    })
    if (res.status === 0 && res.data) {
      childrenList.value = res.data
      // 创建儿童ID到儿童信息的映射
      const map = new Map()
      res.data.forEach((child) => {
        map.set(child.id, {
          id: child.id,
          name: child.title || child.name,
          classId: child.classId
        })
      })
      childrenMap.value = map
    }
  } catch (error) {
    console.error('获取儿童列表失败:', error)
  }
}

const getList = async () => {
  isLoading.value = false
  paging.currentPage = 1
  let res = await getParentList({
    ...paging
  })
  if (res.data) {
    // 为每个家长添加对应的儿童信息
    listdata.data = res.data.map((parent) => {
      // 这里需要根据实际的数据结构来关联儿童信息
      // 如果家长数据中有childId字段，可以直接使用
      // 否则可能需要其他方式来关联
      return {
        ...parent,
        childInfo: null // 暂时设为null，等待进一步的数据结构确认
      }
    })
  }
  isLoading.value = true
}

const onConfirmUpdateParent = () => {
  listdata.parentItemData = {}
  getList()
}
onLoad(async (options) => {
  if (options.classId) {
    paging.classId = options.classId
    currentClassId.value = options.classId
    // 先获取儿童列表，再获取家长列表
    await getChildrenData()
    await getList()
  }
})
onShow(() => {
  checks()
})
// 小程序分享页面
onShareAppMessage(() => sharePageObj())
</script>

<style lang="scss" scoped>
.custom-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88rpx); /* 减去导航栏高度 */
  position: relative;
  overflow: hidden;
}

// 家长列表
.parent-list {
  flex: 1;
  overflow: hidden;
  position: relative;

  .parent-scroll {
    height: 100%;
    padding-bottom: 120rpx; /* 为底部按钮预留空间 */
    box-sizing: border-box;
  }
}

.parent-container {
  padding: 20rpx;
}
.user-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 28rpx;
  background: #ffffff;
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx #eee;

  .user-avatar {
    margin-right: 24rpx;

    image {
      width: 88rpx;
      height: 88rpx;
      border-radius: 20rpx;
    }
  }

  .user-info {
    flex: 1;

    .user-header {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .user-name {
        font-size: 30rpx;
        font-weight: 600;
        margin-right: 12rpx;
        color: #333;
      }

      .user-note {
        font-size: 22rpx;
        color: #808080;
        font-weight: 500;
      }
    }

    .user-relation {
      font-size: 24rpx;
      color: #808080;
    }
  }

  .user-action {
    margin-left: 12rpx;
  }
}
// 加载更多状态
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;

  .loading-text {
    font-size: 26rpx;
    color: #666;
  }
}

// 没有更多了
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .no-more-text {
    font-size: 26rpx;
    color: #999;
  }
}

// 底部按钮
.action-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

// 弹窗样式 - 与circleV1保持一致
.pop-item {
  @include selflex(x, start, center);
  height: 88upx;

  .act {
    margin-left: 30upx;
    font-size: 28upx;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(51, 51, 51, 1);
  }
}

// 删除确认弹窗
.modlCont {
  text-align: center;
}

.child-selector {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .child-list {
    max-height: 60vh;
    padding: 0 32rpx;
  }

  .child-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }

    .child-avatar {
      margin-right: 24rpx;

      image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
      }
    }

    .child-info {
      flex: 1;

      .child-name {
        display: block;
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }

      .child-gender {
        font-size: 24rpx;
        color: #808080;
      }
    }
  }
}
</style>
